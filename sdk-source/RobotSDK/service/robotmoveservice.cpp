#include "robotmoveservice.h"
#include <math.h>
#include <string.h>

#include "globalutil.h"
#include "robotutilservice.h"
#include "protoencodedecode.h"
#include "robotcontrolservices.h"

#define  INVALID_SOCKET  (~0)           //无效的Socket

#define DEFALT_END_LINE_VELC      (0.03)               //末端速度　　单位　米每秒
#define DEFALT_END_LINE_ACC       (0.03)               //末端加速度　单位　米每秒方
#define DEFALT_END_ANGLE_VELC     (100.0/180.0*M_PI)   //末端角速度　　单位　弧度每秒
#define DEFALT_END_ANGLE_ACC      (100.0/180.0*M_PI)   //末端角加速度　单位　弧度每秒方
#define DEFALT_JERK_ACC_RATIO     (0.5)

#define DEFALT_JOINT_VELC    (25.0/180.0*M_PI)    //关节速度　　单位　弧度每秒
#define DEFALT_JOINT_ACC     (25.0/180.0*M_PI)    //关节及速度　单位　弧度每秒方
#define DEFALT_BLEND_RADIUS  (2.0/100.0)          //交融半径单位　米

#define MAX_BLEND_RADIUS     (5.01/100.0)          //最大交融半径单位　米

#define MAX_END_LINE_VELC         (5.01)           //最大末端速度　　　单位米
#define MAX_END_LINE_ACC          (80.01)          //最大末端加速度　　　单位米
#define MAX_END_ANGLE_VELC   (180.01/180.0*M_PI)   //最大末端角速度　　　　单位　弧度每秒
#define MAX_END_ANGLE_ACC    (180.01/180.0*M_PI)   //最大末端角加速度　　　　单位　弧度每秒方
#define MAX_JOINT_VELC       (500.01/180.0*M_PI)   //最大关节速度　　　单位　弧度每秒
#define MAX_JOINT_ACC        (9600.01/180.0*M_PI)   //最大关节加速度　　单位　弧度每秒方


#define WAYPOINT_VECTOR_MAX_LEN 12000

using namespace aubo_robot_namespace;             //使用命名空间
using namespace aubo_robot_logtrace;

extern bool IkfuncCheckJointRangeOfMotion();
extern void IkfuncSetJointRangeOfMotion(const double* rangeOfMotion);
/**
 * @brief 判断3点是否能以moveP的方式运动
 * @param start: 起始路点
 * @param middle: 中间路点
 * @param end: 结束路点
 * @return 如果不能运动则返回false, 否则返回true
 */
bool checkMovePFeasibility(const aubo_robot_namespace::wayPoint_S& start, const aubo_robot_namespace::wayPoint_S& middle,
                                  const aubo_robot_namespace::wayPoint_S& end)
{
    //! 判断指标和算法库代码保持一致
    bool ret = true;
    double line1_s = 0, line2_s = 0;
    cartesianPos_U dVec1, dVec2;

    line1_s = 0;
    for (int i = 0; i < 3; i++)
    {
        dVec1.positionVector[i] = start.cartPos.positionVector[i] - middle.cartPos.positionVector[i]; //unnormalized x-axis of arc coordinate.
        line1_s += dVec1.positionVector[i] * dVec1.positionVector[i];
    }
    line1_s = sqrt(line1_s); // length of line 1

    line2_s = 0;
    for (int j = 0; j < 3; j++)
    {
        dVec2.positionVector[j] = end.cartPos.positionVector[j] - middle.cartPos.positionVector[j];
        line2_s += dVec2.positionVector[j] * dVec2.positionVector[j];
    }
    line2_s = sqrt(line2_s);// length of line 2


    double temp = -(dVec1.position.x * dVec2.position.x + dVec1.position.y * dVec2.position.y +
             dVec1.position.z * dVec2.position.z) / (line1_s * line2_s);

    if (fabs(temp) > 0.9999) //0.1 degree
        ret = false;

    return ret;
}

//double                                         RobotMoveService::s_moveProfileEndMaxLineAcc;       //运动属性中末端型运动的最大线加速度

//double                                         RobotMoveService::s_moveProfileEndMaxLineVelc;      //运动属性中末端型运动的最大线速度

//double                                         RobotMoveService::s_moveProfileEndMaxAngleAcc;      //运动属性中末端型运动的最大角加速度

//double                                         RobotMoveService::s_moveProfileEndMaxAngleVelc;     //运动属性中末端型运动的最大角速度

//double                                         RobotMoveService::s_moveProfileJerkAccRatio = DEFALT_JERK_ACC_RATIO;

//aubo_robot_namespace::JointVelcAccParam        RobotMoveService::s_moveProfileJointMaxAcc;         //运动属性中关节型运动的最大加速度

//aubo_robot_namespace::JointVelcAccParam        RobotMoveService::s_moveProfileJointMaxVelc;        //运动属性中关节型运动的最大速度

//std::vector<aubo_robot_namespace::wayPoint_S>  RobotMoveService::s_moveWayPointVector;             //运动轨迹中的点

//aubo_robot_namespace::CoordCalibrateByJointAngleAndTool   RobotMoveService::s_teachMoveCoordinateSystem;     //运动属性中示教坐标系属性

//RobotMoveService::MoveRelativeParam_t          RobotMoveService::s_moveProfileRelativeParam;       //运动属性中的偏移属性

//double                                         RobotMoveService::s_moveProfileBlendRadius;         //运动属性中的交融半径

//double                                         RobotMoveService::s_moveProfileTrackPlaybackCycle;  //运动属性中的轨迹回放周期

//int                                            RobotMoveService::s_moveProfileCircularLoopTimes;   //运动属性中的圆轨迹的运动圈数

//bool                                           RobotMoveService::s_moveProfileEnableIterIk;        //运动属性中的是否可以使用迭代近似逆解

//aubo_robot_namespace::ToolInEndDesc            RobotMoveService::s_moveProfileToolInEndDesc;       //运动属性中的工具描述

//bool                                           RobotMoveService::s_moveProfileToolTrack;

//RobotMoveService::MoveArrivalAhead_t           RobotMoveService::s_moveArrivalAhead;

//bool                                           RobotMoveService::s_moveProfileIsInited = false;

//RobotMoveService::offlineTrackRecognition      RobotMoveService::s_offlineTrackRecognition;



RobotMoveService::RobotMoveService(RobotControlServices *p)
{
    m_robotBaseService = p;

    //由于运动属性是全局共享的，所以只初始化一次
    if(s_moveProfileIsInited == false)
    {
        initClassProfile();

        s_moveProfileIsInited = true;
    }
}


int RobotMoveService::initClassProfile()
{
    int ret = initMoveProfile();

    s_moveProfileToolInEndDesc.toolInEndPosition.x = 0;
    s_moveProfileToolInEndDesc.toolInEndPosition.y = 0;
    s_moveProfileToolInEndDesc.toolInEndPosition.z = 0;
    s_moveProfileToolInEndDesc.toolInEndOrientation.w = 1;
    s_moveProfileToolInEndDesc.toolInEndOrientation.x = 0;
    s_moveProfileToolInEndDesc.toolInEndOrientation.y = 0;
    s_moveProfileToolInEndDesc.toolInEndOrientation.z = 0;
    s_moveProfileToolTrack = false;

    return ret;
}



int RobotMoveService::moveProfileAndWaypointsFormatCommunication(RobotCommandType commandType, const RobotMoveProfile &moveProfile, const std::vector<wayPoint_S> &wayPointVector)
{
    int   ret;

    char *protobufTextPtr = NULL;

    int   protobufTextLength = 0;

    if( ProtoEncodeDecode::getRequest_robotMove(&protobufTextPtr, &protobufTextLength, moveProfile, wayPointVector) == true)
    {
//        printf("--------------------------protobufTextLength-----------------------------------------------------\n");
//        for(int i=0; i<protobufTextLength;i++)
//        {
//            printf(" %x     ",protobufTextPtr[i]);
//            if(i==10)
//            {
//                printf("\n");
//            }
//        }
//        printf("\n--------------------------protobufTextLength---------------------------------------------------\n");
//        printf("protobufTextLength:  %d\n",protobufTextLength);
        ret = m_robotBaseService->requestServiceOnlyCheckSendResultMode(commandType, protobufTextPtr, protobufTextLength);

        if(ret != ErrnoSucc)
        {
            W_ERROR("sdk log: Move interface call, function-sendRequestCheckResponseResult return false. ret=%d", ret);
        }
    }
    else
    {
        ret = ErrCode_CreateMotionRequestFailed;

        W_ERROR("sdk log: Create move request failed.");
    }

    return ret;
}



int RobotMoveService::moveBaseService(RobotMoveService::RobotMoveClass MoveType, const RobotMoveProfile &moveProfile,
                                      const std::vector<wayPoint_S> &wayPointVector, int &moveEndEventType, bool IsBolck)
{
    int ret = ErrCode_Failed;

    int condionWaitRet = 0;

    moveEndEventType = RobotEventInvalid;

    if(IsBolck == false)     /** 非阻塞形式调用 **/
    {
        W_INFO("sdk log: User non-blocking call motion interface.");

        if(MoveType == ROBOT_MOVE_CLASS_GENARAL)
        {
            ret = moveProfileAndWaypointsFormatCommunication(CommunicationMateType::CommunicationType_RobotMove, moveProfile, wayPointVector);
        }
        else if(MoveType == ROBOT_MOVE_CLASS_OFFLINE_TRACK)
        {
            ret = offlineTrackStartupBaseService();
        }
        else if(MoveType == ROBOT_MOVE_CLASS_OFFLINE_TRACK_RECOGNITION)
        {
            ret = m_robotBaseService->startupOfflineExcitTrajService(s_offlineTrackRecognition.trackName.c_str(), (aubo_robot_namespace::Robot_Dyn_identify_traj)s_offlineTrackRecognition.type, s_offlineTrackRecognition.subType);
        }
        else
        {
            W_ERROR("sdk log:Move class not exist.");
        }
    }
    else      // 阻塞模式
    {
        W_INFO("sdk log: User blocking call motion interface.");

        m_robotBaseService->setMoveFinishEventType(RobotEventInvalid);

        m_robotBaseService->clearMoveFinishEventQueue();

        if(MoveType == ROBOT_MOVE_CLASS_GENARAL)
        {
            ret = moveProfileAndWaypointsFormatCommunication(CommunicationMateType::CommunicationType_RobotMove, moveProfile, wayPointVector);
        }
        else if(MoveType == ROBOT_MOVE_CLASS_OFFLINE_TRACK)
        {
            ret = offlineTrackStartupBaseService();
        }
        else if(MoveType == ROBOT_MOVE_CLASS_OFFLINE_TRACK_RECOGNITION)
        {
            ret = m_robotBaseService->startupOfflineExcitTrajService(s_offlineTrackRecognition.trackName.c_str(), (aubo_robot_namespace::Robot_Dyn_identify_traj)s_offlineTrackRecognition.type, s_offlineTrackRecognition.subType);
        }
        else
        {
            W_ERROR("sdk log: Move class not exist.");
        }

        pthread_mutex_t *moveAtTrackTargetPosMutex    = m_robotBaseService->getRobotMoveAtTrackTargetPosMutexPtr();

        pthread_cond_t  *moveAtTrackTargetPosConditon = m_robotBaseService->getRobotMoveAtTrackTargetPosConditonPtr();

        if(ret == ErrnoSucc)
        {
            pthread_mutex_lock(moveAtTrackTargetPosMutex);

            if(m_robotBaseService->frontToMoveFinishEventQueue() == aubo_robot_namespace::RobotEvent_atTrackTargetPos)
            {
                //说明到位信号已经先于响应回复到来
                W_INFO("sdk log: atTrackTargetPos singal before the response appears");

                pthread_mutex_unlock(moveAtTrackTargetPosMutex);       //临界区数据操作完毕，释放互斥锁
            }
            else
            {
                m_robotBaseService->clearMoveFinishEventQueue();

                condionWaitRet = pthread_cond_wait(moveAtTrackTargetPosConditon, moveAtTrackTargetPosMutex); // 通过互斥量等待等待到位信号

                if(condionWaitRet == 0)
                {
                    aubo_robot_namespace::RobotEventType eventType = m_robotBaseService->frontToMoveFinishEventQueue();

                    moveEndEventType = eventType;

                    if(eventType == aubo_robot_namespace::RobotEvent_atTrackTargetPos)
                    {
                        //正常到位
                        ret = ErrnoSucc;

                        W_INFO("sdk log: Success wait for the signal to arrive target positon.");
                    }
                    else if(eventType == aubo_robot_namespace::RobotEvent_socketDisconnected)
                    {
                        //网络连接断开
                        ret = ErrCode_SocketDisconnect;

                        W_ERROR("sdk log: Network connection has been disconnected.");
                    }
                    else if(eventType == RobotEvent_robotControllerError)
                    {
                        //版本兼容  控制器异常事件
                        ret = ErrCode_robotControllerError;

                        W_ERROR("sdk log: Motion interrupted by controller abnormal .");
                    }
                    else if(eventType >= RobotEventMoveJConfigError && eventType < RobotEventMoveEnterStopState)
                    {
                        //控制器异常事件
                        ret = eventType+20000;

                        W_ERROR("sdk log: Motion is interrupted by controller event. function return:%d eventType=%d",ret, eventType);
                    }
                    else if(eventType == RobotEvent_collision)
                    {
                        //碰撞
                        ret = ErrCode_collision;

                        W_ERROR("sdk log: Motion interrupted by collision.");
                    }
                    else if(eventType >= RobotEventHardwareErrorNotify && eventType <= RobotEventHardwareErrorNotifyMaximumIndex)
                    {
                        //硬件异常事件
                        ret = eventType+20000;

                        W_ERROR("sdk log: Motion is interrupted by hardware event. function return:%d eventType=%d",ret, eventType);
                    }
                    else if(eventType == aubo_robot_namespace::RobotEventMoveEnterStopState)   //正常到位
                    {
                        //运动中进入了stop状态
                        ret = ErrCodeMoveEnterStopState;

                        W_ERROR("sdk log: Motion is interrupted by stop the event. function return:%d",ret);

                    }
                    else
                    {
                        //未知
                        ret = ErrCodeMoveInterruptedByEvent;

                        W_ERROR("sdk log: Motion is interrupted by event. function return:%d eventType=%d",ret, eventType);
                    }
                }
                else
                {
                    ret = ErrCode_MotionRelatedVariableError;

                    W_ERROR("sdk log: Call robotMoveInterface  pthread_cond_wait failed.");
                }

                pthread_mutex_unlock(moveAtTrackTargetPosMutex);       //临界区数据操作完毕，释放互斥锁
            }
        }
        else
        {
            W_ERROR("sdk log: robotMoveInterface return false, because server return false.");

            pthread_mutex_unlock(moveAtTrackTargetPosMutex);
        }
    }

    W_INFO("sdk log: User call motion interface finish.");

    return ret;
}




int RobotMoveService::robotMoveService(const RobotMoveProfile &moveProfile, const std::vector<wayPoint_S> &wayPointVector, int &moveEndEventType, bool isBolck)
{
    int ret = moveBaseService(ROBOT_MOVE_CLASS_GENARAL, moveProfile, wayPointVector, moveEndEventType, isBolck);

    return ret;
}

int RobotMoveService::robotMoveControl(RobotMoveControlCommand cmd)
{
    int   ret = ErrnoSucc;

    switch(cmd)
    {
        case RobotMoveStop:
            W_INFO("sdk log: user ready call move slow stop.");
            ret = m_robotBaseService->robotMoveControlService(CommunicationMateType::CommunicationType_RobotMoveControlType_SlowStop);
            W_INFO("sdk log: user call move slow stop finish.");
            break;
        case RobotMovePause:
            W_INFO("sdk log: user ready call move pause.");
            ret = m_robotBaseService->robotMoveControlService(CommunicationMateType::CommunicationType_RobotMoveControlType_Pause);
            W_INFO("sdk log: user call move pause finish.");
            break;
        case RobotMoveContinue:
            W_INFO("sdk log: user ready call move continue.");
            ret = m_robotBaseService->robotMoveControlService(CommunicationMateType::CommunicationType_RobotMoveControlType_Continue);
            W_INFO("sdk log: user call move continue finish.");
            break;
        default:
            break;
    }

    return ret;
}

int RobotMoveService::robotMoveFastStop()
{
    return m_robotBaseService->robotMoveControlService(CommunicationMateType::CommunicationType_RobotMoveControlType_FastStop);
}

int RobotMoveService::robotMoveSlowStop(bool isBoardStopEvent)
{
    if(isBoardStopEvent)
    {
        W_INFO("sdk log: user ready call move slow stop and board event.");
        return m_robotBaseService->robotMoveControlService(CommunicationMateType::CommunicationType_RobotMoveControlType_SlowStop);
        W_INFO("sdk log: user call move slow stop  and board event finish.");
    }
    else
    {
        return robotMoveControl(RobotMoveStop);
    }
}

int RobotMoveService::setReducePara(const double jerkRatio, const double acc[], int size)
{
    int   ret;
    char *protobufTextPtr = NULL;
    int   protobufTextLength = 0;

    if(size!=6)
    {
        return ErrCode_ParamError;
    }

    if(!(jerkRatio>=0.1 && jerkRatio<=20))
    {
        return ErrCode_ParamError;
    }

    std::vector<double> paramVeror;
    paramVeror.clear();
    paramVeror.push_back(jerkRatio);
    for(int i=0;i<6;i++)
        paramVeror.push_back(acc[i]);

    if( ProtoEncodeDecode::getRequest_doubleTypeValueVector(&protobufTextPtr, &protobufTextLength, paramVeror) == true )
    {
        ret = m_robotBaseService->requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_setReduceParameter, protobufTextPtr, protobufTextLength);
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make setReducePara protobuf content failed.");
    }

    return ret;
}


int RobotMoveService::robotTeachMove(const RobotMoveProfile &moveProfile, const CoordCalibrateByJointAngleAndTool &userCoordSystem)
{
    int   ret;

    char *protobufTextPtr = NULL;

    int   protobufTextLength = 0;

    std::vector<wayPoint_S> wayPointVector;

    wayPointVector.clear();

    if( ProtoEncodeDecode::getRequest_robotTeachMove(&protobufTextPtr, &protobufTextLength, moveProfile, userCoordSystem) == true)
    {
        ret = m_robotBaseService->requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_RobotTeachMoveStart, protobufTextPtr, protobufTextLength);
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

         W_ERROR("sdk log: Create robotTeachStart request failed.");
    }

    return ret;
}

int RobotMoveService::robotTeachStop()
{
    return m_robotBaseService->requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_RobotTeachMoveStop, NULL, 0);
}


int RobotMoveService::offlineTrackStartupBaseService()
{
    return m_robotBaseService->requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_OfflineTrackMoveStartup, NULL, 0);
}

int RobotMoveService::offlineTrackMoveStartupService(bool isBolck)
{
    int  ret = false;

    RobotMoveProfile moveProfile;

    std::vector<wayPoint_S> wayPointVector;

    initDefaultMoveProfile(moveProfile);

    wayPointVector.clear();

    int moveEndEventType = RobotEventInvalid;

    ret =  moveBaseService(ROBOT_MOVE_CLASS_OFFLINE_TRACK, moveProfile, wayPointVector, moveEndEventType, isBolck);

    return ret;
}

int RobotMoveService::offlineTrackMoveStop()
{
    return m_robotBaseService->requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_OfflineTrackMoveStop, NULL, 0);
}


int RobotMoveService::offlineTrackWaypointClear()
{
    return m_robotBaseService->requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_OfflineTrackWaypointClear, NULL, 0);
}


int RobotMoveService::offlineTrackWaypointAppend(const std::vector<wayPoint_S> &wayPointVector)
{
    RobotMoveProfile moveProfile;

    memset(&moveProfile, 0, sizeof(moveProfile));

    return moveProfileAndWaypointsFormatCommunication(CommunicationMateType::CommunicationType_OfflineTrackWaypointAppend, moveProfile, wayPointVector);
}

int RobotMoveService::startupOfflineExcitTraj(const char *trackFile, Robot_Dyn_identify_traj type, int subtype, bool isBolck)
{
    s_offlineTrackRecognition.trackName = trackFile;
    s_offlineTrackRecognition.type = type;
    s_offlineTrackRecognition.subType = subtype;

    int  ret = false;

    RobotMoveProfile moveProfile;

    std::vector<wayPoint_S> wayPointVector;

    initDefaultMoveProfile(moveProfile);

    wayPointVector.clear();

    int moveEndEventType = RobotEventInvalid;

    ret =  moveBaseService(ROBOT_MOVE_CLASS_OFFLINE_TRACK_RECOGNITION, moveProfile, wayPointVector, moveEndEventType, isBolck);

    return ret;
}

int RobotMoveService::enterTcp2CanbusMode()
{
    return m_robotBaseService->requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_EnterTcp2CanbusMode, NULL, 0);
}

int RobotMoveService::leaveTcp2CanbusMode()
{
    return m_robotBaseService->requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_LeaveTcp2CanbusMode, NULL, 0);
}

int RobotMoveService::setRobotPosData2Canbus(const std::vector<wayPoint_S> &wayPointVector)
{
    RobotMoveProfile moveProfile;

    memset(&moveProfile, 0, sizeof(moveProfile));

    return moveProfileAndWaypointsFormatCommunication(CommunicationMateType::CommunicationType_SetRobotPosData2Canbus, moveProfile, wayPointVector);
}

int RobotMoveService::enterRobotReduceMode()
{
    return m_robotBaseService->requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_enterRobotReduceMode, NULL, 0);

}

int RobotMoveService::exitRobotReduceMode()
{
    return m_robotBaseService->requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_exitRobotReduceMode, NULL, 0);

}

int RobotMoveService::initMoveProfile()
{
    s_moveWayPointVector.clear();

    s_moveProfileEndMaxLineAcc  = DEFALT_END_LINE_ACC;        //末端型最大线加速度　单位:米每秒方
    s_moveProfileEndMaxLineVelc = DEFALT_END_LINE_VELC;       //末端型最大线速度　　单位:米每秒
    s_moveProfileEndMaxAngleAcc  = DEFALT_END_ANGLE_ACC;      //末端型最大角加速度　单位:弧度每秒方
    s_moveProfileEndMaxAngleVelc = DEFALT_END_ANGLE_VELC;     //末端型最大角速度　　单位:弧度每秒
    //s_moveProfileJerkAccRatio    = DEFALT_JERK_ACC_RATIO;

    for(int i=0;i<ARM_DOF;i++)
    {
        s_moveProfileJointMaxAcc.jointPara[i]  = DEFALT_JOINT_ACC;    //单位　　弧度每秒方
        s_moveProfileJointMaxVelc.jointPara[i] = DEFALT_JOINT_VELC;   //单位　　弧度每秒
    }

    //初始化运动属性中的示教平面
    s_teachMoveCoordinateSystem.coordType = BaseCoordinate;

    //初始化运动属性中的偏移属性
    s_moveProfileRelativeParam.isEnable = false;
    s_moveProfileRelativeParam.coordinateSystem.coordType = BaseCoordinate;
    s_moveProfileRelativeParam.relativePosition[0] = 0.0;
    s_moveProfileRelativeParam.relativePosition[1] = 0.0;
    s_moveProfileRelativeParam.relativePosition[2] = 0.0;
    s_moveProfileRelativeParam.relativeOrientation.orientation.w=1.0;
    s_moveProfileRelativeParam.relativeOrientation.orientation.x=0.0;
    s_moveProfileRelativeParam.relativeOrientation.orientation.y=0.0;
    s_moveProfileRelativeParam.relativeOrientation.orientation.z=0.0;


    //初始化运动属性中的交融半径
    s_moveProfileBlendRadius = DEFALT_BLEND_RADIUS;

    s_moveProfileTrackPlaybackCycle = 0.1;

    //初始化运动属性中的圆轨迹的运动圈数
    s_moveProfileCircularLoopTimes = 0;

    //初始化运动属性中的是否可以使用迭代近似逆解
    s_moveProfileEnableIterIk      = false;

    s_moveArrivalAhead.arrivalAheadStat = arrival_ahead_none;

    s_moveArrivalAhead.arrivalAheadThr  = 0.0;

    return ErrnoSucc;
}


int RobotMoveService::setMoveJointMaxAcc(const JointVelcAccParam &moveMaxAcc)
{
    int ret = ErrnoSucc;

    for(int i=0; i<ARM_DOF; i++)
    {
        if((moveMaxAcc.jointPara[i]>0.0 && moveMaxAcc.jointPara[i] <= MAX_JOINT_ACC)==false )
        {
            ret = ErrCode_ParamError;

            W_ERROR("user moveMaxAcc=%f",moveMaxAcc.jointPara[i]);

            break;
        }
    }

    if(ret == ErrnoSucc)
    {
        s_moveProfileJointMaxAcc = moveMaxAcc;
    }

    return ret;
}

int RobotMoveService::setMoveJointMaxVelc(const JointVelcAccParam &moveMaxVelc)
{
    int ret = ErrnoSucc;

    for(int i=0; i<ARM_DOF; i++)
    {
        if((moveMaxVelc.jointPara[i]>0.0 && moveMaxVelc.jointPara[i] <= MAX_JOINT_VELC)==false )
        {
            ret = ErrCode_ParamError;

            break;
        }
    }

    if(ret == ErrnoSucc)
    {
        s_moveProfileJointMaxVelc = moveMaxVelc;
    }

    return ret;
}

void RobotMoveService::getMoveJointMaxAcc(JointVelcAccParam &moveMaxAcc)
{
    moveMaxAcc = s_moveProfileJointMaxAcc;
}

void RobotMoveService::getMoveJointMaxVelc(JointVelcAccParam &moveMaxVelc)
{
    moveMaxVelc = s_moveProfileJointMaxVelc;
}

int RobotMoveService::setMoveEndMaxLineAcc(double moveMaxAcc)
{
    int ret = ErrnoSucc;

    if(moveMaxAcc>0.0 && moveMaxAcc <= MAX_END_LINE_ACC )
    {
        s_moveProfileEndMaxLineAcc = moveMaxAcc;
    }
    else
    {
        ret = ErrCode_ParamError;
    }

    return ret;
}

int RobotMoveService::setMoveEndMaxLineVelc(double moveMaxVelc)
{
    int ret = ErrnoSucc;

    if(moveMaxVelc>0.0 && moveMaxVelc <= MAX_END_LINE_VELC )
    {
        s_moveProfileEndMaxLineVelc = moveMaxVelc;
    }
    else
    {
        ret = ErrCode_ParamError;
    }

    return ret;
}


void RobotMoveService::getMoveEndMaxLineAcc(double &moveMaxAcc)
{
    moveMaxAcc = s_moveProfileEndMaxLineAcc;
}

void RobotMoveService::getMoveEndMaxLineVelc(double &moveMaxVelc)
{
    moveMaxVelc = s_moveProfileEndMaxLineVelc;
}

int RobotMoveService::setMoveEndMaxAngleAcc(double moveMaxAcc)
{
    int ret = ErrnoSucc;

    if(moveMaxAcc>0.0 && moveMaxAcc <= MAX_END_ANGLE_ACC )
    {
        s_moveProfileEndMaxAngleAcc = moveMaxAcc;
    }
    else
    {
        ret = ErrCode_ParamError;
    }

    return ret;
}

int RobotMoveService::setMoveEndMaxAngleVelc(double moveMaxVelc)
{
    int ret = ErrnoSucc;

    if(moveMaxVelc>0.0 && moveMaxVelc <= MAX_END_ANGLE_VELC )
    {
        s_moveProfileEndMaxAngleVelc = moveMaxVelc;
    }
    else
    {
        ret = ErrCode_ParamError;
    }

    return ret;
}

void RobotMoveService::getMoveEndMaxAngleAcc(double &moveMaxAcc)
{
    moveMaxAcc = s_moveProfileEndMaxAngleAcc;
}

void RobotMoveService::getMoveEndMaxAngleVelc(double &moveMaxVelc)
{
    moveMaxVelc = s_moveProfileEndMaxAngleVelc;
}

int RobotMoveService::setJerkAccRatio(double acc)
{
    int ret = ErrnoSucc;

    if(acc>=0.0 && acc < 1.00001 )
    {
        s_moveProfileJerkAccRatio = acc;
    }
    else
    {
        ret = ErrCode_ParamError;
    }

    return ret;
}

void RobotMoveService::getJerkAccRatio(double &acc)
{
    acc = s_moveProfileJerkAccRatio;
}

void RobotMoveService::clearWayPointVector()
{
    s_moveWayPointVector.clear();
}

int RobotMoveService::addWayPoint(const wayPoint_S &wayPoint)
{
    int ret = ErrCode_MotionWaypointVetorSizeError;

    if(s_moveWayPointVector.size()<WAYPOINT_VECTOR_MAX_LEN)
    {
        s_moveWayPointVector.push_back(wayPoint);

        //edit 20211123 wpy
//       if(s_moveWayPointVector.size() >= 3)
//       {
//           if(checkMovePFeasibility(s_moveWayPointVector[s_moveWayPointVector.size() - 3],s_moveWayPointVector[s_moveWayPointVector.size() - 2],s_moveWayPointVector[s_moveWayPointVector.size() - 1]) == false)
//           {

//                 s_moveWayPointVector.pop_back();
//                 s_moveWayPointVector.pop_back();

//                 s_moveWayPointVector.push_back(wayPoint);

//                 std::cout << "The point are at line " <<std::endl;
//           }

//       }
      /****************************************************************************/

        ret = ErrnoSucc;
    }

    return ret;
}


void RobotMoveService::getWayPointVector(std::vector<wayPoint_S> &wayPointVector)
{
    wayPointVector.clear();

    for(int i=0;i<(int)s_moveWayPointVector.size();i++)
    {
        wayPointVector.push_back(s_moveWayPointVector[i]);
    }
}


float RobotMoveService::getBlendRadius()
{
    return s_moveProfileBlendRadius;
}

int RobotMoveService::setBlendRadius(float value)
{
    int ret = ErrnoSucc;

    if(value>0.0 && value<MAX_BLEND_RADIUS)
    {
        s_moveProfileBlendRadius = value;
    }
    else
    {
        ret = ErrCode_ParamError;
    }

    return ret;
}

int RobotMoveService::getCircularLoopTimes()
{
    return s_moveProfileCircularLoopTimes;
}

void RobotMoveService::setCircularLoopTimes(int times)
{
    s_moveProfileCircularLoopTimes = times;
}

double RobotMoveService::getTrackPlaybackCycle()
{
    return s_moveProfileTrackPlaybackCycle;
}

void RobotMoveService::setTrackPlaybackCycle(double second)
{
    s_moveProfileTrackPlaybackCycle = second;
}

void RobotMoveService::setEnableIterIk(bool value)
{
    s_moveProfileEnableIterIk = value;
}

int RobotMoveService::setMoveProfileRelativeParam(const MoveRelative &relativeMoveOnBase)
{
    CoordCalibrateByJointAngleAndTool coordSystem;

    coordSystem.coordType = BaseCoordinate;

    int ret = setMoveProfileRelativeParam(relativeMoveOnBase, coordSystem);

    return ret;
}


int RobotMoveService::setMoveProfileRelativeParam(const MoveRelative &relativeMoveOnUserCoord,
                                                  const CoordCalibrateByJointAngleAndTool &userCoord)
{
    int ret = ErrnoSucc;

    s_moveProfileRelativeParam.isEnable = relativeMoveOnUserCoord.ena;
    s_moveProfileRelativeParam.coordinateSystem = userCoord;
    s_moveProfileRelativeParam.relativeOrientation.orientation = relativeMoveOnUserCoord.relativeOri;

    for(int i=0;i<3;i++)
    {
        s_moveProfileRelativeParam.relativePosition[i] = relativeMoveOnUserCoord.relativePosition[i];
    }

    return ret;
}


int  RobotMoveService::setTeachCoordinateSystem(const CoordCalibrateByJointAngleAndTool &coordSystem)
{
    s_teachMoveCoordinateSystem  = coordSystem;

    return ErrnoSucc;
}

int RobotMoveService::setMoveProfileToolParamIsNone()
{
    s_moveProfileToolInEndDesc.toolInEndPosition.x = 0;
    s_moveProfileToolInEndDesc.toolInEndPosition.y = 0;
    s_moveProfileToolInEndDesc.toolInEndPosition.z = 0;
    s_moveProfileToolInEndDesc.toolInEndOrientation.w = 1;
    s_moveProfileToolInEndDesc.toolInEndOrientation.x = 0;
    s_moveProfileToolInEndDesc.toolInEndOrientation.y = 0;
    s_moveProfileToolInEndDesc.toolInEndOrientation.z = 0;

    s_moveProfileToolTrack = false;

    return ErrnoSucc;
}

int RobotMoveService::setMoveProfileToolParam(const ToolInEndDesc &toolInEndDesc)
{
    s_moveProfileToolInEndDesc = toolInEndDesc;

    s_moveProfileToolTrack = true;

    return ErrnoSucc;
}

int RobotMoveService::getMoveProfileToolParam(ToolInEndDesc &toolInEndDesc)
{
    toolInEndDesc = s_moveProfileToolInEndDesc;

    return ErrnoSucc;
}

int RobotMoveService::setNoArrivalAhead()
{
    s_moveArrivalAhead.arrivalAheadStat = arrival_ahead_none;

    s_moveArrivalAhead.arrivalAheadThr  = 0.0;

    return ErrnoSucc;
}

int RobotMoveService::setArrivalAheadDistanceMode(double distance)
{
    s_moveArrivalAhead.arrivalAheadStat = arrival_ahead_distance;

    s_moveArrivalAhead.arrivalAheadThr  = distance;

    return ErrnoSucc;
}

int RobotMoveService::setArrivalAheadTimeMode(double second)
{
    s_moveArrivalAhead.arrivalAheadStat = arrival_ahead_time;

    s_moveArrivalAhead.arrivalAheadThr  = second;

    return ErrnoSucc;
}

int RobotMoveService::setArrivalAheadBlendDistanceMode(double distance)
{
    s_moveArrivalAhead.arrivalAheadStat = arrival_ahead_blend_distance;

    s_moveArrivalAhead.arrivalAheadThr  = distance;

    return ErrnoSucc;
}

int RobotMoveService::robotServiceUploadWayPointToController()
{
    int ret = ErrCode_Failed;

    RobotMoveProfile  moveProfile;

    if(s_moveWayPointVector.size() >= 3)
    {
        /** 0:运动属性初始化 **/
        getCurrentMoveProfileRemoveOffset(MODEP, CARTESIAN_MOVEP, NO_TEACH, moveProfile);

        modifyOffsetProfileByWaypointAndGlobalOffset(s_moveWayPointVector[0], moveProfile);

        RobotMoveProfile moveProfile;

        memset(&moveProfile, 0, sizeof(moveProfile));


        W_INFO("sdk log: Ready to upload waypoints.");

        /** 1:调用接口　**/
        ret = moveProfileAndWaypointsFormatCommunication(CommunicationMateType::CommunicationType_uploadWayPointToController, moveProfile, s_moveWayPointVector);

        W_INFO("sdk log: Upload waypoint end.");
    }
    else
    {
        ret = ErrCode_MotionWaypointVetorSizeError;

        W_ERROR("sdk log: Number of waypoints less than 3.");
    }

    return ret;
}


int RobotMoveService::robotJointMove(wayPoint_S &wayPoint, bool IsBolck)
{
    int ret = ErrCode_Failed;

    RobotMoveProfile  moveProfile;

    std::vector<aubo_robot_namespace::wayPoint_S>  wayPointVector;

    /** 0:运动属性初始化 **/
    getCurrentMoveProfileRemoveOffset(MODEJ, NO_TRACK, NO_TEACH, moveProfile);

    modifyOffsetProfileByWaypointAndGlobalOffset(wayPoint, moveProfile);

    /** 1:设置运动的路点　**/
    wayPointVector.clear();
    wayPointVector.push_back(wayPoint);

    int moveEndEventType = RobotEventInvalid;

    /** 2:调用运动接口　**/
    ret = robotMoveService(moveProfile, wayPointVector, moveEndEventType, IsBolck);

    return ret;
}

int RobotMoveService::robotJointMove(MoveProfile_t &moveProfile, wayPoint_S &wayPoint, bool IsBolck)
{
    int ret = ErrCode_Failed;

    RobotMoveProfile  robotMoveProfile;

    std::vector<aubo_robot_namespace::wayPoint_S>  wayPointVector;

    /** 0:运动属性初始化 **/
    MoveProfileConvert(MODEJ, wayPoint, moveProfile, robotMoveProfile);

    /** 1:设置运动的路点　**/
    wayPointVector.clear();
    wayPointVector.push_back(wayPoint);

    int moveEndEventType = RobotEventInvalid;

    //TODO...
//    W_INFO("moveProfile.moveMode= %d", robotMoveProfile.moveMode);
//    W_INFO("moveProfile.relativeMove enable=%d position=%.5f,%.5f,%.5f, ori=%.5f,%.5f,%.5f,%.5f", robotMoveProfile.relativeMove.ena,
//                                       robotMoveProfile.relativeMove.relativePosition[0],
//                                       robotMoveProfile.relativeMove.relativePosition[1],
//                                       robotMoveProfile.relativeMove.relativePosition[2],
//                                       robotMoveProfile.relativeMove.relativeOrientation.orientation.w,
//                                       robotMoveProfile.relativeMove.relativeOrientation.orientation.x,
//                                       robotMoveProfile.relativeMove.relativeOrientation.orientation.y,
//                                       robotMoveProfile.relativeMove.relativeOrientation.orientation.z);
//    W_INFO("moveProfile.tool position=%.5f,%.5f,%.5f, ori=%.5f,%.5f,%.5f,%.5f",
//           robotMoveProfile.toolInEndPosition.position.x, robotMoveProfile.toolInEndPosition.position.y, robotMoveProfile.toolInEndPosition.position.z,
//           robotMoveProfile.toolInEndOrientation.w, robotMoveProfile.toolInEndOrientation.x, robotMoveProfile.toolInEndOrientation.y, robotMoveProfile.toolInEndOrientation.z);


    /** 2:调用运动接口　**/
    ret = robotMoveService(robotMoveProfile, wayPointVector, moveEndEventType, IsBolck);

    return ret;
}


int RobotMoveService::robotFollowModeJointMove(wayPoint_S &wayPoint)
{
    int ret = ErrCode_Failed;

    RobotMoveProfile  moveProfile;

    std::vector<aubo_robot_namespace::wayPoint_S>  wayPointVector;

    /** 0:运动属性初始化 **/
    getCurrentMoveProfileRemoveOffset(MODEJ, TRACKING, NO_TEACH, moveProfile);

    modifyOffsetProfileByWaypointAndGlobalOffset(wayPoint, moveProfile);

    /** 1:设置运动的路点　**/
    wayPointVector.clear();
    wayPointVector.push_back(wayPoint);

    int moveEndEventType = RobotEventInvalid;

    /** 2:调用运动接口　**/
    ret = robotMoveService(moveProfile, wayPointVector, moveEndEventType, false);

    return ret;
}


int RobotMoveService::robotLineMove(wayPoint_S &wayPoint, bool IsBolck)
{
    int ret = ErrCode_Failed;

    RobotMoveProfile  moveProfile;

    std::vector<aubo_robot_namespace::wayPoint_S>  wayPointVector;

    /** 0:运动属性初始化 **/
    getCurrentMoveProfileRemoveOffset(MODEL, NO_TRACK, NO_TEACH, moveProfile);

    modifyOffsetProfileByWaypointAndGlobalOffset(wayPoint, moveProfile);

    /** 1:设置运动的路点　**/
    wayPointVector.clear();
    wayPointVector.push_back(wayPoint);

    int moveEndEventType = RobotEventInvalid;

    //TODO...
//    W_INFO("moveProfile.moveMode= %d", moveProfile.moveMode);
//    W_INFO("moveProfile.relativeMove enable=%d position=%.5f,%.5f,%.5f, ori=%.5f,%.5f,%.5f,%.5f", moveProfile.relativeMove.ena,
//                                       moveProfile.relativeMove.relativePosition[0],
//                                       moveProfile.relativeMove.relativePosition[1],
//                                       moveProfile.relativeMove.relativePosition[2],
//                                       moveProfile.relativeMove.relativeOrientation.orientation.w,
//                                       moveProfile.relativeMove.relativeOrientation.orientation.x,
//                                       moveProfile.relativeMove.relativeOrientation.orientation.y,
//                                       moveProfile.relativeMove.relativeOrientation.orientation.z);
//    W_INFO("moveProfile.tool position=%.5f,%.5f,%.5f, ori=%.5f,%.5f,%.5f,%.5f",
//           moveProfile.toolInEndPosition.position.x, moveProfile.toolInEndPosition.position.y, moveProfile.toolInEndPosition.position.z,
//           moveProfile.toolInEndOrientation.w, moveProfile.toolInEndOrientation.x, moveProfile.toolInEndOrientation.y, moveProfile.toolInEndOrientation.z);

    /** 2:调用运动接口　**/
    ret = robotMoveService( moveProfile, wayPointVector, moveEndEventType, IsBolck);

    return ret;
}

int RobotMoveService::robotLineMove(MoveProfile_t &moveProfile, wayPoint_S &wayPoint, bool IsBolck)
{
    int ret = ErrCode_Failed;

    RobotMoveProfile  robotMoveProfile;

    std::vector<aubo_robot_namespace::wayPoint_S>  wayPointVector;

    /** 0:运动属性初始化 **/
    MoveProfileConvert(MODEL, wayPoint, moveProfile, robotMoveProfile);

    /** 1:设置运动的路点　**/
    wayPointVector.clear();
    wayPointVector.push_back(wayPoint);

    int moveEndEventType = RobotEventInvalid;

    /** 2:调用运动接口　**/
    ret = robotMoveService(robotMoveProfile, wayPointVector, moveEndEventType, IsBolck);

    return ret;
}

int RobotMoveService::robotGetRotateAxisUserToBase(const aubo_robot_namespace::Ori &oriOnUserCoord,const double rotateAxisOnUserCoord[], double rotateAxisOnBaseCoord[])
{
    // 四元数转换到旋转矩阵
    double Rb_t[9] = {0.};
    double axis_base[3] = {0.};
    double qw = oriOnUserCoord.w;
    double qx = oriOnUserCoord.x;
    double qy = oriOnUserCoord.y;
    double qz = oriOnUserCoord.z;
    double n = 1.0f/sqrt(qx*qx+qy*qy+qz*qz+qw*qw);
    qw *= n;
    qx *= n;
    qy *= n;
    qz *= n;
    Rb_t[0] = 1.0f - 2.0f*qy*qy - 2.0f*qz*qz;  Rb_t[1] = 2.0f*qx*qy - 2.0f*qz*qw;         Rb_t[2] = 2.0f*qx*qz + 2.0f*qy*qw;
    Rb_t[3] = 2.0f*qx*qy + 2.0f*qz*qw;         Rb_t[4] = 1.0f - 2.0f*qx*qx - 2.0f*qz*qz;  Rb_t[5] = 2.0f*qy*qz - 2.0f*qx*qw;
    Rb_t[6] = 2.0f*qx*qz - 2.0f*qy*qw;         Rb_t[7] = 2.0f*qy*qz + 2.0f*qx*qw;         Rb_t[8] = 1.0f - 2.0f*qx*qx - 2.0f*qy*qy;
    // 旋转轴变换到base
    for(unsigned int i = 0; i < 3; i++)
    {
        for(unsigned int j = 0; j < 3; j++)
        {
            axis_base[i] += Rb_t[3 * i + j] * rotateAxisOnUserCoord[j];
        }
    }
    rotateAxisOnBaseCoord[0]=axis_base[0];
    rotateAxisOnBaseCoord[1]=axis_base[1];
    rotateAxisOnBaseCoord[2]=axis_base[2];
    return ErrnoSucc;
}

int RobotMoveService::robotGetRotateTargetWaypiont(const aubo_robot_namespace::wayPoint_S &originatePointOnBaseCoord,const double rotateAxisOnBaseCoord[], double rotateAngle, aubo_robot_namespace::wayPoint_S &targetWayPointOnBaseCoord)
{

    targetWayPointOnBaseCoord=originatePointOnBaseCoord;

    targetWayPointOnBaseCoord.orientation.x = rotateAxisOnBaseCoord[0];
    targetWayPointOnBaseCoord.orientation.y = rotateAxisOnBaseCoord[1];
    targetWayPointOnBaseCoord.orientation.z = rotateAxisOnBaseCoord[2];
    targetWayPointOnBaseCoord.orientation.w = rotateAngle;
    return ErrnoSucc;
}

int RobotMoveService::robotLineRotateMove(const aubo_robot_namespace::wayPoint_S &targetWayPointOnBaseCoord,bool IsBolck)
{
    RobotMoveProfile  moveProfile;
    aubo_robot_namespace::wayPoint_S currentWayPointOnBaseCoord;    //基于基座标系下的当前路点信息
    //首先将机械臂当前位置的路点信息，该路点信息(currentWayPoint)是基于基座标系的
    getCurrnetRoadPoint(currentWayPointOnBaseCoord);

    std::vector<aubo_robot_namespace::wayPoint_S>  wayPointVector;
    /** 0:运动属性初始化 **/
    getCurrentMoveProfileRemoveOffset(MODEL, NO_TRACK, ROT_Y, moveProfile);
    modifyOffsetProfileByWaypointAndGlobalOffset(currentWayPointOnBaseCoord, moveProfile);
    /** 1:设置运动的路点　**/
    wayPointVector.clear();
    wayPointVector.push_back(targetWayPointOnBaseCoord);

    int moveEndEventType = RobotEventInvalid;

    /** 2:调用运动接口　**/
    return robotMoveService( moveProfile, wayPointVector, moveEndEventType, IsBolck);
}


int RobotMoveService::robotLineRotateMove(const aubo_robot_namespace::CoordCalibrateByJointAngleAndTool &userCoord, const double rotateAxisOnUserCoord[], double rotateAngle, bool IsBolck)
{
    int ret = ErrCode_Failed;

    double baseRotateAxis[3] = {0};

    RobotMoveProfile  moveProfile;
    aubo_robot_namespace::wayPoint_S currentWayPointOnBaseCoord;    //基于基座标系下的当前路点信息

    //首先将机械臂当前位置的路点信息，该路点信息(currentWayPoint)是基于基座标系的
    getCurrnetRoadPoint(currentWayPointOnBaseCoord);

    std::vector<aubo_robot_namespace::wayPoint_S>  wayPointVector;

    /** 0:运动属性初始化 **/
    getCurrentMoveProfileRemoveOffset(MODEL, NO_TRACK, ROT_Y, moveProfile);

    modifyOffsetProfileByWaypointAndGlobalOffset(currentWayPointOnBaseCoord, moveProfile);

    ret = RobotUtilService::offsetVectorUserCoord2Base(rotateAxisOnUserCoord, userCoord, &baseRotateAxis[0]);

    if(ret == ErrnoSucc)
    {
        /** 1:设置运动的路点　**/
        wayPointVector.clear();
        currentWayPointOnBaseCoord.orientation.x = baseRotateAxis[0];
        currentWayPointOnBaseCoord.orientation.y = baseRotateAxis[1];
        currentWayPointOnBaseCoord.orientation.z = baseRotateAxis[2];
        currentWayPointOnBaseCoord.orientation.w = rotateAngle;
        wayPointVector.push_back(currentWayPointOnBaseCoord);

        int moveEndEventType = RobotEventInvalid;

        /** 2:调用运动接口　**/
        ret = robotMoveService( moveProfile, wayPointVector, moveEndEventType, IsBolck);
    }
    else
    {
        if(userCoord.coordType == EndCoordinate)
        {
            // 四元数转换到旋转矩阵
            double Rb_t[9] = {0.};
            double axis_base[3] = {0.};
            Ori Qb_t = currentWayPointOnBaseCoord.orientation;
            double qw = Qb_t.w;
            double qx = Qb_t.x;
            double qy = Qb_t.y;
            double qz = Qb_t.z;
            double n = 1.0f/sqrt(qx*qx+qy*qy+qz*qz+qw*qw);
            qw *= n;
            qx *= n;
            qy *= n;
            qz *= n;
            Rb_t[0] = 1.0f - 2.0f*qy*qy - 2.0f*qz*qz;  Rb_t[1] = 2.0f*qx*qy - 2.0f*qz*qw;         Rb_t[2] = 2.0f*qx*qz + 2.0f*qy*qw;
            Rb_t[3] = 2.0f*qx*qy + 2.0f*qz*qw;         Rb_t[4] = 1.0f - 2.0f*qx*qx - 2.0f*qz*qz;  Rb_t[5] = 2.0f*qy*qz - 2.0f*qx*qw;
            Rb_t[6] = 2.0f*qx*qz - 2.0f*qy*qw;         Rb_t[7] = 2.0f*qy*qz + 2.0f*qx*qw;         Rb_t[8] = 1.0f - 2.0f*qx*qx - 2.0f*qy*qy;
            // 旋转轴变换到base
            for(unsigned int i = 0; i < 3; i++)
            {
                for(unsigned int j = 0; j < 3; j++)
                {
                    axis_base[i] += Rb_t[3 * i + j] * rotateAxisOnUserCoord[j];
                }
            }

            /** 1:设置运动的路点　**/
            wayPointVector.clear();
            currentWayPointOnBaseCoord.orientation.x = axis_base[0];
            currentWayPointOnBaseCoord.orientation.y = axis_base[1];
            currentWayPointOnBaseCoord.orientation.z = axis_base[2];
            currentWayPointOnBaseCoord.orientation.w = rotateAngle;
            wayPointVector.push_back(currentWayPointOnBaseCoord);

            int moveEndEventType = RobotEventInvalid;

            /** 2:调用运动接口　**/
            ret = robotMoveService( moveProfile, wayPointVector, moveEndEventType, IsBolck);
        }
        else
        {
            std::cout<<"baseRotateAxis"<<baseRotateAxis[0]<<","<<baseRotateAxis[1]<<","<<baseRotateAxis[2]<<std::endl;
        }
    }

    return ret;
}


int  RobotMoveService::robotTrackMove(move_track subMoveMode, bool IsBolck)
{
    int ret = ErrCode_Failed;

    RobotMoveProfile  moveProfile;

    /**********************************/
    //edit 20211123 wpy
    if(s_moveWayPointVector.size() == 2)
    {

        std::cout << "there is 2 point " << "moveP will be MoveL " <<std::endl;
        ret = robotLineMove(s_moveWayPointVector[1],IsBolck);
        return ret;
    }

     /***************************************/

    if(s_moveWayPointVector.size() >= 3)
    {
        /** 0:运动属性初始化 **/
        getCurrentMoveProfileRemoveOffset(MODEP, subMoveMode, NO_TEACH, moveProfile);

        modifyOffsetProfileByWaypointAndGlobalOffset(s_moveWayPointVector[0], moveProfile);

        int moveEndEventType = RobotEventInvalid;

        /** 1:调用接口　**/
        ret = robotMoveService(moveProfile, s_moveWayPointVector, moveEndEventType, IsBolck);
    }
    else
    {
        ret = ErrCode_MotionWaypointVetorSizeError;

        W_ERROR("sdk log: Number of waypoints less than 3.");
    }

    return ret;
}

int RobotMoveService::robotTrackMoveModeEx(move_track subMoveMode, bool IsBolck)
{
    int ret = ErrCode_Failed;

    RobotMoveProfile  moveProfile;

    if(s_moveWayPointVector.size() >= 3)
    {
        /** 0:运动属性初始化 **/
        getCurrentMoveProfileRemoveOffset(MODEP, subMoveMode, NO_TEACH, moveProfile);

        modifyOffsetProfileByWaypointAndGlobalOffset(s_moveWayPointVector[0], moveProfile);

        int moveEndEventType = RobotEventInvalid;

        std::vector<aubo_robot_namespace::wayPoint_S> moveWayPointVector;

        /** 1:调用接口　**/
        ret = robotMoveService(moveProfile, moveWayPointVector, moveEndEventType, IsBolck);
    }
    else
    {
        ret = ErrCode_MotionWaypointVetorSizeError;

        W_ERROR("sdk log: Number of waypoints less than 3.");
    }

    return ret;
}


int RobotMoveService::robotTeachStart(teach_mode teachMode, bool direction)
{
    int ret = ErrCode_Failed;

    RobotMoveProfile  moveProfile;

    /** 运动属性初始化 **/
    move_mode moveMode = MODEJ;

    switch(teachMode)
    {
    case JOINT1: case JOINT2: case JOINT3:
    case JOINT4: case JOINT5: case JOINT6:
        moveMode  = MODEJ;
        break;

    case MOV_X:  case MOV_Y:  case MOV_Z:
        moveMode  = MODEL;
        break;

    case ROT_X:  case ROT_Y:  case ROT_Z:
        moveMode  = MODEL;
        break;

    default:
        break;
    }

    /** 0:运动属性初始化 **/
    getCurrentMoveProfileRemoveOffset(moveMode, TRACKING, teachMode, moveProfile);

    if(direction == true)
    {
        moveProfile.circularLoopTimes = 1;     //这里复用moveProfile中的circularLoopTimes属性为示教的方向
    }
    else
    {
        moveProfile.circularLoopTimes = 0;     //这里复用moveProfile中的circularLoopTimes属性为示教的方向
    }

    /** 1:调用接口　**/
    ret = robotTeachMove(moveProfile, s_teachMoveCoordinateSystem );

    return ret;
}



int RobotMoveService::robotMoveToTargetPositionByRelative(move_mode moveMode, const CoordCalibrateByJointAngleAndTool &userCoordSystem,
                                                          const ToolInEndDesc &toolInEndDesc,
                                                          const MoveRelative &relativeOnUser, bool IsBolck)
{
    int ret = ErrCode_Failed;

    aubo_robot_namespace::wayPoint_S sourceWayPointOnBaseCoord;    //基于基座标系下的源路点信息
    aubo_robot_namespace::wayPoint_S targetWayPointOnBaseCoord;    //基于基座标系下的目标路点信息

    //首先将机械臂当前位置的路点信息，该路点信息(currentWayPoint)是基于基座标系的
    getRoadPointFromController(sourceWayPointOnBaseCoord);

    //获取目标路点
    ret = getTargetWaypointByRelative(sourceWayPointOnBaseCoord, userCoordSystem, toolInEndDesc, relativeOnUser, targetWayPointOnBaseCoord);

    if(ret == ErrnoSucc)
    {
        if(MODEJ == moveMode)
        {
            ret = robotJointMove(targetWayPointOnBaseCoord, IsBolck);
        }
        else if(MODEL == moveMode)
        {
            ret = robotLineMove(targetWayPointOnBaseCoord,  IsBolck);
        }
        else
        {
            W_ERROR("sdk log: robotMoveToTargetPosition move mode error.");
        }
    }

    return ret;
}

int RobotMoveService::getJointAngleByTargetPositionKeepCurrentOri(const CoordCalibrateByJointAngleAndTool &userCoordSystem, const Pos &position,
                                                                  const ToolInEndDesc &toolInEndDesc, wayPoint_S &targetWayPointOnBaseCoord)
{
    int ret = ErrCode_Failed;

    aubo_robot_namespace::wayPoint_S sourceWayPointOnBaseCoord;    //基于基座标系下的源路点信息

    //首先将机械臂当前位置的路点信息，该路点信息(currentWayPoint)是基于基座标系的
    getCurrnetRoadPoint(sourceWayPointOnBaseCoord);

    ret = getTargetWaypointByPosition(sourceWayPointOnBaseCoord, userCoordSystem, position, toolInEndDesc,targetWayPointOnBaseCoord);

    return ret;
}


int RobotMoveService::robotMoveToTargetPositionByPosition(move_mode moveMode, const CoordCalibrateByJointAngleAndTool &userCoordSystem,
                                                          const Pos &position, const ToolInEndDesc &toolInEndDesc, bool IsBolck)
{
    int ret = ErrCode_Failed;

    aubo_robot_namespace::wayPoint_S sourceWayPointOnBaseCoord;    //基于基座标系下的源路点信息
    aubo_robot_namespace::wayPoint_S targetWayPointOnBaseCoord;    //基于基座标系下的目标路点信息

    //首先将机械臂当前位置的路点信息，该路点信息(currentWayPoint)是基于基座标系的
    getCurrnetRoadPoint(sourceWayPointOnBaseCoord);

    ret = getTargetWaypointByPosition(sourceWayPointOnBaseCoord, userCoordSystem, position, toolInEndDesc,targetWayPointOnBaseCoord);

    //获取目标路点
    if(ret == ErrnoSucc)
    {
        if(MODEJ == moveMode)
        {
            ret = robotJointMove(targetWayPointOnBaseCoord,  IsBolck);
        }
        else if(MODEL == moveMode)
        {
            ret = robotLineMove(targetWayPointOnBaseCoord,  IsBolck);
        }
        else
        {
            W_ERROR("sdk log: robotMoveToTargetPosition move mode error.");
        }
    }

    return ret;
}

int RobotMoveService::setWeaveMoveParameters(const WeaveMove &weaveMove)
{
    int   ret;
    char *protobufTextPtr = NULL;
    int   protobufTextLength = 0;

    std::vector<double> doubleTypeValueVector;
    doubleTypeValueVector.clear();
    doubleTypeValueVector.push_back((weaveMove.weaveEnable)? 1.0:0.0);
    doubleTypeValueVector.push_back(weaveMove.weaveType);
    doubleTypeValueVector.push_back(weaveMove.weaveStep);
    doubleTypeValueVector.push_back(weaveMove.weaveAmplitude);
    doubleTypeValueVector.push_back(weaveMove.weaveHoldDistance);
    doubleTypeValueVector.push_back(weaveMove.weaveAngle);

    if( ProtoEncodeDecode::getRequest_doubleTypeValueVector(&protobufTextPtr, &protobufTextLength, doubleTypeValueVector) == true )
    {
        ret = m_robotBaseService->requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_setWeaveMoveParameters, protobufTextPtr, protobufTextLength);
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make CommunicationType_setWeaveMoveParameters protobuf content failed.");
    }

    return ret;
}

int RobotMoveService::setJointRangeOfMotion(const JointRangeOfMotion &rangeOfMotion)
{
    int   ret;
    char *protobufTextPtr = NULL;
    int   protobufTextLength = 0;

    std::vector<double> doubleTypeValueVector;
    doubleTypeValueVector.clear();
    doubleTypeValueVector.push_back((rangeOfMotion.enable)? 1.0:0.0);
    for(int i=0;i<ARM_DOF;i++)
    {
        doubleTypeValueVector.push_back(rangeOfMotion.rangeValues[i].minValue);
        doubleTypeValueVector.push_back(rangeOfMotion.rangeValues[i].maxValue);
    }

    if( ProtoEncodeDecode::getRequest_doubleTypeValueVector(&protobufTextPtr, &protobufTextLength, doubleTypeValueVector) == true )
    {
        ret = m_robotBaseService->requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_setJointRangeOfMotion, protobufTextPtr, protobufTextLength);
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make CommunicationType_setJointRangeOfMotion protobuf content failed.");
    }

    return ret;
}

int RobotMoveService::getJointRangeOfMotion(JointRangeOfMotion &rangeOfMotion)
{
    int   ret;

    CommunicationResponse robotResponse;

    ret = m_robotBaseService->requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_getJointRangeOfMotion, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        std::vector<double> doubleTypeValueVector;

        if(ProtoEncodeDecode::resolveResponse_doubleTypeValueVector(robotResponse.m_textPtr,robotResponse.m_textLength, doubleTypeValueVector ,errorCode)==true)
        {
            ret = m_robotBaseService->getErrCodeByServerResponse(errorCode);

            if(ret == ErrnoSucc)
            {
                rangeOfMotion.enable = (doubleTypeValueVector[0]>0.0)? true:false;

                for(int i=0; i<ARM_DOF; i++)
                {
                    rangeOfMotion.rangeValues[i].minValue = doubleTypeValueVector[(i*2)+1];
                    rangeOfMotion.rangeValues[i].maxValue = doubleTypeValueVector[(i*2)+2];
                }
            }
        }
        else
        {
            ret = ErrCode_CreateRequestFailed;

            W_ERROR("sdk log: Resolve EthernetDeviceName response failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}

int RobotMoveService::getJointPositionLimit(JointRangeOfMotion &rangeOfMotion)
{
    int   ret;

    CommunicationResponse robotResponse;

    ret = m_robotBaseService->requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_getJointPositionLimit, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        std::vector<double> doubleTypeValueVector;

        if(ProtoEncodeDecode::resolveResponse_doubleTypeValueVector(robotResponse.m_textPtr,robotResponse.m_textLength, doubleTypeValueVector ,errorCode)==true)
        {
            ret = m_robotBaseService->getErrCodeByServerResponse(errorCode);

            if(ret == ErrnoSucc)
            {
                rangeOfMotion.enable = (doubleTypeValueVector[0]>0.0)? true:false;

                for(int i=0; i<ARM_DOF; i++)
                {
                    rangeOfMotion.rangeValues[i].minValue = doubleTypeValueVector[(i*2)+1];
                    rangeOfMotion.rangeValues[i].maxValue = doubleTypeValueVector[(i*2)+2];
                }

                // Joint range is not enabled(is invaild)
                if (!IkfuncCheckJointRangeOfMotion()) {
                    W_WARN("Use joint position limit(physical) as joint range of ikfunc");
                    double range[12];
                    for(unsigned int i = 0; i < 6; i++)
                    {
                        range[2 * i] = rangeOfMotion.rangeValues[i].minValue;
                        range[2 * i + 1] = rangeOfMotion.rangeValues[i].maxValue;
                    }
                    IkfuncSetJointRangeOfMotion(range);
                }
            }
        }
        else
        {
            ret = ErrCode_CreateRequestFailed;

            W_ERROR("sdk log: Resolve EthernetDeviceName response failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}

int RobotMoveService::getRegulateSpeedModeConfig(RegulateSpeedModeParamConfig_t &regulateSpeedModeParam)
{
    int   ret;

    CommunicationResponse robotResponse;

    ret = m_robotBaseService->requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_getRegulateSpeedModeConfig, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        std::vector<double> doubleTypeValueVector;

        if(ProtoEncodeDecode::resolveResponse_doubleTypeValueVector(robotResponse.m_textPtr,robotResponse.m_textLength, doubleTypeValueVector ,errorCode)==true)
        {
            if((ret=m_robotBaseService->getErrCodeByServerResponse(errorCode)) == ErrnoSucc && doubleTypeValueVector.size()==ARM_DOF*2+2)
            {
                for(int i=0;i<ARM_DOF;i++)
                {
                    regulateSpeedModeParam.maxJointVelocity[i]     = doubleTypeValueVector.at(i);
                    regulateSpeedModeParam.maxJointAcceleration[i] = doubleTypeValueVector.at(i+ARM_DOF);
                }
                regulateSpeedModeParam.maxTcpVelocity = doubleTypeValueVector.at(ARM_DOF*2);
                regulateSpeedModeParam.maxTcpAcceleration = doubleTypeValueVector.at(ARM_DOF*2+1);
            }
        }
        else
        {
            ret = ErrCode_CreateRequestFailed;

            W_ERROR("sdk log: Resolve RegulateSpeedModeConfig response failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}

int RobotMoveService::setRegulateSpeedModeConfig(const RegulateSpeedModeParamConfig_t &regulateSpeedModeParam)
{
    int   ret;
    char *protobufTextPtr = NULL;
    int   protobufTextLength = 0;
    std::vector<double> doubleTypeValueVector;

    for(int i=0;i<ARM_DOF;i++)  doubleTypeValueVector.push_back(regulateSpeedModeParam.maxJointVelocity[i]);
    for(int i=0;i<ARM_DOF;i++)  doubleTypeValueVector.push_back(regulateSpeedModeParam.maxJointAcceleration[i]);
    doubleTypeValueVector.push_back(regulateSpeedModeParam.maxTcpVelocity);
    doubleTypeValueVector.push_back(regulateSpeedModeParam.maxTcpAcceleration);

    if( ProtoEncodeDecode::getRequest_doubleTypeValueVector(&protobufTextPtr, &protobufTextLength, doubleTypeValueVector) == true )
    {
        ret = m_robotBaseService->requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_setRegulateSpeedModeConfig, protobufTextPtr, protobufTextLength);
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make CommunicationType_setRegulateSpeedModeConfig protobuf content failed.");
    }

    return ret;
}



int RobotMoveService::enableRegulateSpeedMode(bool enbaleFlag)
{
    int   ret;
    char *protobufTextPtr = NULL;
    int   protobufTextLength = 0;
    std::vector<int> intTypeValueVector;

    intTypeValueVector.push_back((enbaleFlag)? 1:0);

    if( ProtoEncodeDecode::getRequest_intTypeValueVector(&protobufTextPtr, &protobufTextLength, intTypeValueVector) == true )
    {
        ret = m_robotBaseService->requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_enableRegulateSpeedMode, protobufTextPtr, protobufTextLength);
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make CommunicationType_enableRegulateSpeedMode protobuf content failed.");
    }

    return ret;
}

int RobotMoveService::getForceControlModeAdmittancePatam(AdmittancePatam_t &admittancePatam)
{
    int   ret;

    CommunicationResponse robotResponse;

    // 做实时版本与非实时兼容
    if (m_robotBaseService->getVersionCode() > 4010000) {
        ret = m_robotBaseService->requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_getForceControlModeAdmittancePatam_V4010000, NULL, 0, robotResponse);
    } else {
        ret = m_robotBaseService->requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_getForceControlModeAdmittancePatam, NULL, 0, robotResponse);
    }

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        std::vector<double> doubleTypeValueVector;

        if(ProtoEncodeDecode::resolveResponse_doubleTypeValueVector(robotResponse.m_textPtr,robotResponse.m_textLength, doubleTypeValueVector ,errorCode)==true)
        {
            if((ret=m_robotBaseService->getErrCodeByServerResponse(errorCode)) == ErrnoSucc && doubleTypeValueVector.size()==6*3)
            {
                for(int i=0;i<6;i++)
                {
                    admittancePatam.inertia[i] = doubleTypeValueVector.at(i);
                    admittancePatam.damping[i] = doubleTypeValueVector.at(1*6+i);
                    admittancePatam.stiffness[i] = doubleTypeValueVector.at(2*6+i);
                }
            }
        }
        else
        {
            ret = ErrCode_CreateRequestFailed;

            W_ERROR("sdk log: Resolve RegulateSpeedModeConfig response failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}

int RobotMoveService::setForceControlModeAdmittancePatam(const AdmittancePatam_t &admittancePatam)
{
    int   ret;
    char *protobufTextPtr = NULL;
    int   protobufTextLength = 0;
    std::vector<double> doubleTypeValueVector;

    for(int i=0;i<6;i++)  doubleTypeValueVector.push_back(admittancePatam.inertia[i]);
    for(int i=0;i<6;i++)  doubleTypeValueVector.push_back(admittancePatam.damping[i]);
    for(int i=0;i<6;i++)  doubleTypeValueVector.push_back(admittancePatam.stiffness[i]);

    if( ProtoEncodeDecode::getRequest_doubleTypeValueVector(&protobufTextPtr, &protobufTextLength, doubleTypeValueVector) == true )
    {
        // 做实时版本与非实时兼容
        if (m_robotBaseService->getVersionCode() > 4010000) {
            ret = m_robotBaseService->requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_setForceControlModeAdmittancePatam_V4010000, protobufTextPtr, protobufTextLength);
        } else {
            ret = m_robotBaseService->requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_setForceControlModeAdmittancePatam, protobufTextPtr, protobufTextLength);
        }
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make CommunicationType_setRegulateSpeedModeConfig protobuf content failed.");
    }

    return ret;
}

int RobotMoveService::getForceControlModeExploreForceParam(double &forceLimit, double &distLimit)
{
    int   ret;

    CommunicationResponse robotResponse;

    // 做实时版本与非实时兼容
    if (m_robotBaseService->getVersionCode() > 4010000) {
        ret = m_robotBaseService->requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_getForceControlModeExploreForceParam_V4010000, NULL, 0, robotResponse);
    } else {
        ret = m_robotBaseService->requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_getForceControlModeExploreForceParam, NULL, 0, robotResponse);
    }

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        std::vector<double> doubleTypeValueVector;

        if(ProtoEncodeDecode::resolveResponse_doubleTypeValueVector(robotResponse.m_textPtr,robotResponse.m_textLength, doubleTypeValueVector, errorCode)==true)
        {
            if((ret=m_robotBaseService->getErrCodeByServerResponse(errorCode)) == ErrnoSucc)
            {
                if(doubleTypeValueVector.size()==2)
                {
                    forceLimit = doubleTypeValueVector.at(0);
                    distLimit  = doubleTypeValueVector.at(1);
                }
                else
                {
                    ret = ErrCode_ResolveResponseFailed;
                }
            }
        }
        else
        {
            ret = ErrCode_CreateRequestFailed;

            W_ERROR("sdk log: Resolve ExploreForceParam response failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}

int RobotMoveService::setForceControlModeExploreForceParam(double forceLimit, double distLimit)
{
    int   ret;
    char *protobufTextPtr = NULL;
    int   protobufTextLength = 0;
    std::vector<double> doubleTypeValueVector;

    doubleTypeValueVector.push_back(forceLimit);
    doubleTypeValueVector.push_back(distLimit);

    if( ProtoEncodeDecode::getRequest_doubleTypeValueVector(&protobufTextPtr, &protobufTextLength, doubleTypeValueVector) == true )
    {
        // 做实时版本与非实时兼容
        if (m_robotBaseService->getVersionCode() > 4010000) {
            ret = m_robotBaseService->requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_setForceControlModeExploreForceParam_V4010000, protobufTextPtr, protobufTextLength);
        } else {
            ret = m_robotBaseService->requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_setForceControlModeExploreForceParam, protobufTextPtr, protobufTextLength);
        }
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make CommunicationType_setForceControlModeExploreForceParam protobuf content failed.");
    }

    return ret;
}

int RobotMoveService::enableForceControlModeService(bool enbaleFlag)
{
    int   ret;
    char *protobufTextPtr = NULL;
    int   protobufTextLength = 0;
    std::vector<int> intTypeValueVector;

    intTypeValueVector.push_back((enbaleFlag)? 1:0);

    if( ProtoEncodeDecode::getRequest_intTypeValueVector(&protobufTextPtr, &protobufTextLength, intTypeValueVector) == true )
    {
        // 做实时版本与非实时兼容
        if (m_robotBaseService->getVersionCode() > 4010000) {
            ret = m_robotBaseService->requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_enableForceControlMode_V4010000, protobufTextPtr, protobufTextLength);
        } else {
            ret = m_robotBaseService->requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_enableForceControlMode, protobufTextPtr, protobufTextLength);
        }
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make CommunicationType_enableForceControlMode protobuf content failed.");
    }

    return ret;
}

int RobotMoveService::getRealtimeForceDataService(double forceData[])
{
    int   ret;

    CommunicationResponse robotResponse;

    ret = m_robotBaseService->requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_getForceSensorData, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        std::vector<double> doubleTypeValueVector;

        if(ProtoEncodeDecode::resolveResponse_doubleTypeValueVector(robotResponse.m_textPtr,robotResponse.m_textLength, doubleTypeValueVector, errorCode)==true)
        {
            if((ret=m_robotBaseService->getErrCodeByServerResponse(errorCode)) == ErrnoSucc)
            {
                if(doubleTypeValueVector.size()==6)
                {
                    for(int i=0;i<6;i++)
                        forceData[i] = doubleTypeValueVector.at(i);
                }
                else
                {
                    ret = ErrCode_ResolveResponseFailed;
                }
            }
        }
        else
        {
            ret = ErrCode_CreateRequestFailed;

            W_ERROR("sdk log: Resolve ExploreForceParam response failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}



int RobotMoveService::getTargetWaypointByRelative(const wayPoint_S &sourceWayPointOnBaseCoord,
                                                  const CoordCalibrateByJointAngleAndTool &userCoordSystem,
                                                  const ToolInEndDesc &toolInEndDesc,
                                                  const MoveRelative &relativeOnUser,
                                                  wayPoint_S &targetWayPointOnBaseCoord)
{
    int ret = ErrnoSucc;

    (void)(toolInEndDesc);

    Pos targetPositionOnBase;
    Ori targetOrientationOnBase;
    double relativeOnBase[3];

    aubo_robot_namespace::Pos endPosition;

    //TODO...
//    W_INFO("ByRelative sourcePoint:%f %f %f %f %f %f",
//           sourceWayPointOnBaseCoord.jointpos[0],sourceWayPointOnBaseCoord.jointpos[1],sourceWayPointOnBaseCoord.jointpos[2],
//           sourceWayPointOnBaseCoord.jointpos[3],sourceWayPointOnBaseCoord.jointpos[4],sourceWayPointOnBaseCoord.jointpos[5]);

//    W_INFO("ByRelative.userCoordSystem coordType=%d methods=%d tool.position=%.5f,%.5f,%.5f, ori=%.5f,%.5f,%.5f,%.5f",
//           userCoordSystem.coordType, userCoordSystem.methods,
//           userCoordSystem.toolDesc.toolInEndPosition.x, userCoordSystem.toolDesc.toolInEndPosition.y, userCoordSystem.toolDesc.toolInEndPosition.z,
//           userCoordSystem.toolDesc.toolInEndOrientation.w, userCoordSystem.toolDesc.toolInEndOrientation.x, userCoordSystem.toolDesc.toolInEndOrientation.y, userCoordSystem.toolDesc.toolInEndOrientation.z);

//    const double *temp = &userCoordSystem.wayPointArray[0].jointPos[0];
//    W_INFO("ByRelative.userCoordSystem point1:%f %f %f %f %f %f", temp[0], temp[1], temp[2], temp[3], temp[4], temp[5]);
//    temp = &(userCoordSystem.wayPointArray[1].jointPos[0]);
//    W_INFO("ByRelative.userCoordSystem point1:%f %f %f %f %f %f", temp[0], temp[1], temp[2], temp[3], temp[4], temp[5]);
//    temp = &(userCoordSystem.wayPointArray[2].jointPos[0]);
//    W_INFO("ByRelative.userCoordSystem point1:%f %f %f %f %f %f", temp[0], temp[1], temp[2], temp[3], temp[4], temp[5]);


//    W_INFO("moveProfile.relativeOnUser enable=%d position=%.5f,%.5f,%.5f, ori=%.5f,%.5f,%.5f,%.5f", relativeOnUser.ena,
//                                       relativeOnUser.relativePosition[0],
//                                       relativeOnUser.relativePosition[1],
//                                       relativeOnUser.relativePosition[2],
//                                       relativeOnUser.relativeOri.w,
//                                       relativeOnUser.relativeOri.x,
//                                       relativeOnUser.relativeOri.y,
//                                       relativeOnUser.relativeOri.z);


    switch(userCoordSystem.coordType)
    {
    case BaseCoordinate:
        relativeOnBase[0] = relativeOnUser.relativePosition[0];
        relativeOnBase[1] = relativeOnUser.relativePosition[1];
        relativeOnBase[2] = relativeOnUser.relativePosition[2];
        targetPositionOnBase.x = sourceWayPointOnBaseCoord.cartPos.position.x +(double)relativeOnBase[0];
        targetPositionOnBase.y = sourceWayPointOnBaseCoord.cartPos.position.y +(double)relativeOnBase[1];
        targetPositionOnBase.z = sourceWayPointOnBaseCoord.cartPos.position.z +(double)relativeOnBase[2];

        targetOrientationOnBase = sourceWayPointOnBaseCoord.orientation;
        break;

    case EndCoordinate:

        endPosition.x = relativeOnUser.relativePosition[0];
        endPosition.y = relativeOnUser.relativePosition[1];
        endPosition.z = relativeOnUser.relativePosition[2];
        RobotUtilService::endPosition2BasePosition(sourceWayPointOnBaseCoord, endPosition, targetPositionOnBase, userCoordSystem.toolDesc.toolInEndOrientation);

        targetOrientationOnBase = sourceWayPointOnBaseCoord.orientation;

        break;

    case WorldCoordinate:
        double tempRelativeOnUser[3];
        tempRelativeOnUser[0] = relativeOnUser.relativePosition[0];
        tempRelativeOnUser[1] = relativeOnUser.relativePosition[1];
        tempRelativeOnUser[2] = relativeOnUser.relativePosition[2];

        //用户坐标系标定测试，检查用户提供的标定参数是否正确
        if( (ret = RobotUtilService::checkCoordinateSystemCalibration(userCoordSystem)) == ErrnoSucc)
        {
            //进行偏移转换
            ret = RobotUtilService::offsetVectorUserCoord2Base(tempRelativeOnUser, userCoordSystem, relativeOnBase);

            if(ret==ErrnoSucc)
            {
                targetPositionOnBase.x = sourceWayPointOnBaseCoord.cartPos.position.x +(double)relativeOnBase[0];
                targetPositionOnBase.y = sourceWayPointOnBaseCoord.cartPos.position.y +(double)relativeOnBase[1];
                targetPositionOnBase.z = sourceWayPointOnBaseCoord.cartPos.position.z +(double)relativeOnBase[2];

                targetOrientationOnBase = sourceWayPointOnBaseCoord.orientation;
            }
        }
        else
        {
            W_ERROR("sdk log: robotMoveToTargetPosition PlaneCalibrate return false.");
        }
        break;
    default:
        ret = ErrCode_ParamError;
        break;
    }


    if(ErrnoSucc == ret)
    {
        //逆解目标位置得到目标关节信息
        ret = RobotUtilService::robotIk(sourceWayPointOnBaseCoord.jointpos,
                                        targetPositionOnBase,
                                        targetOrientationOnBase,
                                        targetWayPointOnBaseCoord);
    }

    return ret;
}



int RobotMoveService::getTargetWaypointByPosition(const wayPoint_S &sourceWayPointOnBaseCoord,
                                                  const CoordCalibrateByJointAngleAndTool &userCoordSystem,
                                                  const Pos &toolEndPosition,
                                                  const ToolInEndDesc &toolInEndDesc,
                                                  wayPoint_S &targetWayPointOnBaseCoord)
{
    /**
     * 函数功能描述:已经起始路点，保持起始点的姿态，根据提供的目标工具末端的位置（在用户坐标系下描述的），得到目标末端点对应的法兰盘中心点的路点（包含：关节角，位置，姿态）
     *
     * 需求: 根据起始点A(基于基座标系下法兰盘中心点)和目标点Ｂ(基于用户坐标系下工具末端点)，　保持起始点A的姿态计算得到目标点C(基于基座标系法兰盘中心点)
     *
     * 分析:　现在我们只有目标点B的位置信息，然而B点是带工具的，去掉工具的时候，必须知道对应B点的姿态信息，
     * 　　　 而B点的姿态与A点相同，所以先将A点转换到对应B点平面上，得到姿态信息；
     *
     *
     * 思路：
     * 　　　1: 将起始点A点通过坐标变换得到A'(基于用户坐标系)　　　A'中含有目标位置B的姿态
     * 　　　2: 上一步得到B点的姿态信息＋已知的B点位置　通过坐标转换得到　B点基于法兰盘中心的位置B'
     *      3: 利用起始点A, B' 逆解求得目标点C
     *
     **/


    int ret = ErrnoSucc;

    Pos  positionOnBase;
    aubo_robot_namespace::Pos sourcePositionOnUserCoord;
    aubo_robot_namespace::Ori sourceOrientationOnUserCoord;

    //1:将起始点A点通过坐标变换得到A'(基于用户坐标系)　　　A'中含有目标位置B的姿态
    if(userCoordSystem.coordType== BaseCoordinate)
    {
        sourceOrientationOnUserCoord = sourceWayPointOnBaseCoord.orientation;
    }
    else if(userCoordSystem.coordType== WorldCoordinate)
    {
        ret = RobotUtilService::base2UserCoordinate(sourceWayPointOnBaseCoord.cartPos.position, sourceWayPointOnBaseCoord.orientation,
                                                    userCoordSystem, toolInEndDesc,
                                                    sourcePositionOnUserCoord, sourceOrientationOnUserCoord);
    }
    else
    {
        ret = ErrCode_ParamError;
    }


    //2: 上一步得到B点的姿态信息＋已知的B点位置　通过坐标转换得到　B点基于法兰盘中心的位置B
    if(ErrnoSucc == ret)
    {
        Ori tempOri;

        //用户转基去工具　　得到目标位置(基于基座标系)
        ret = RobotUtilService::user2BaseCoordinate(toolEndPosition, sourceOrientationOnUserCoord,
                                                    userCoordSystem, toolInEndDesc,
                                                    positionOnBase,  tempOri);
    }


    //3: 利用起始点A, B' 逆解求得目标点C
    if(ErrnoSucc == ret)
    {
        //逆解目标位置得到目标关节信息
        ret = RobotUtilService::robotIk(sourceWayPointOnBaseCoord.jointpos,    //起始点关节角
                                        positionOnBase,                        //目标位置
                                        sourceWayPointOnBaseCoord.orientation, //目标参考姿态
                                        targetWayPointOnBaseCoord);            //逆解结果
    }

    return ret;
}



int RobotMoveService::getCurrnetRoadPoint(aubo_robot_namespace::wayPoint_S &wayPoint)
{
    JointParam jointAngle;

    int ret = m_robotBaseService->getCurrentJointAngle(jointAngle);

    if(ret == ErrnoSucc)
    {
        RobotUtilService::robotFk(jointAngle.jointPos, aubo_robot_namespace::ARM_DOF, wayPoint);
    }
    else
    {
        W_ERROR("sdk log: getCurrentJointAngle error.");
    }

    return ret;
}

int RobotMoveService::getForceSensorData(ForceSensorData &data)
{
    int ret = m_robotBaseService->getForceSensorData(data);
    if(ret == ErrnoSucc)
    {

    }
    else
    {
        W_ERROR("sdk log: getForceSenserData error.");
    }
    return ret;
}



int RobotMoveService::getRoadPointFromController(wayPoint_S &wayPoint)
{
    JointParam jointAngle;

    int ret = m_robotBaseService->getJointAngleFromController(jointAngle);

    if(ret == ErrnoSucc)
    {
        RobotUtilService::robotFk(jointAngle.jointPos, aubo_robot_namespace::ARM_DOF, wayPoint);
    }
    else
    {
        W_ERROR("sdk log: getCurrentJointAngle error.");
    }

    return ret;
}



void RobotMoveService::initDefaultMoveProfile(RobotMoveProfile &moveProfile)
{
    memset(&moveProfile, 0, sizeof(moveProfile));

    //运动属性初始化
    moveProfile.moveMode    = NO_MOVEMODE;
    moveProfile.subMoveMode = NO_TRACK;
    moveProfile.teachMode   = NO_TEACH;
    moveProfile.enableIterIk= false;

    //工具参数
    moveProfile.toolTrack   = false;
    moveProfile.toolInEndPosition.position.x = 0;
    moveProfile.toolInEndPosition.position.y = 0;
    moveProfile.toolInEndPosition.position.z = 0;
    moveProfile.toolInEndOrientation.w = 1;
    moveProfile.toolInEndOrientation.x = 0;
    moveProfile.toolInEndOrientation.y = 0;
    moveProfile.toolInEndOrientation.z = 0;

    moveProfile.blendRadius       = DEFALT_BLEND_RADIUS;    //20cm
    moveProfile.circularLoopTimes = 0;

    //速度加速度
    memset(&moveProfile.maxVelc, 0, sizeof(moveProfile.maxVelc));
    memset(&moveProfile.maxAcc,  0, sizeof(moveProfile.maxAcc));
    for(int i=0;i<aubo_robot_namespace::ARM_DOF;i++)
    {
        moveProfile.maxAcc.jointPara[i]  = DEFALT_JOINT_ACC;      //单位弧度每秒方
        moveProfile.maxVelc.jointPara[i] = DEFALT_JOINT_VELC;     //单位弧度每秒
    }

    //偏移量
    memset(&moveProfile.relativeMove, 0, sizeof(moveProfile.relativeMove));
    moveProfile.relativeMove.ena = false;
    moveProfile.relativeMove.relativeOrientation.orientation.w=1;
    moveProfile.relativeMove.relativeOrientation.orientation.x=0;
    moveProfile.relativeMove.relativeOrientation.orientation.y=0;
    moveProfile.relativeMove.relativeOrientation.orientation.z=0;


    moveProfile.arrivalAhead.arrivalAheadStat = arrival_ahead_none;
    moveProfile.arrivalAhead.arrivalAheadThr  = 0.0;
}

bool RobotMoveService::checkIsJointTypeTrack(const move_mode moveMode, const move_track subMoveMode)
{
    bool isJointTypeFlag = true;

    //关节运动属于　　　关节型运动
    if(moveMode == MODEJ)
    {
        isJointTypeFlag = true;
    }

    //直线运动属于　　　末端型运动
    if(moveMode == MODEL)
    {
        isJointTypeFlag = false;
    }


    if(moveMode == MODEP)
    {
        switch(subMoveMode)
        {
        //cartesian motion for movep　　末端型运动
        case  ARC_CIR:
        case  CARTESIAN_MOVEP:
        case  CARTESIAN_CUBICSPLINE:
        case  CARTESIAN_UBSPLINEINTP:
        case  ORI_POSITION_ROTATE_CIRCUMFERENCE:
              isJointTypeFlag = false;
              break;

            //joint motion  for movep  　　关节型运动
        case  JIONT_CUBICSPLINE:
        case  JOINT_UBSPLINEINTP:
        case  JOINT_GNUBSPLINEINTP:
        case  CARTESIAN_GNUBSPLINEINTP:
        case  CARTESIAN_LOOKAHEAD:
            isJointTypeFlag = true;
            break;

        default:
            break;
        }
    }

    return isJointTypeFlag;
}

void RobotMoveService::getCurrentMoveProfileRemoveOffset(const move_mode moveMode, const move_track subMoveMode, const teach_mode teachMode, RobotMoveProfile &moveProfile)
{
    initDefaultMoveProfile(moveProfile);

    moveProfile.moveMode     = moveMode;
    moveProfile.subMoveMode  = subMoveMode;
    moveProfile.teachMode    = teachMode;

    moveProfile.enableIterIk = s_moveProfileEnableIterIk;
    moveProfile.toolTrack    = s_moveProfileToolTrack;

    moveProfile.blendRadius = s_moveProfileBlendRadius;
    moveProfile.circularLoopTimes = s_moveProfileCircularLoopTimes;  

    //偏移量属性
    memset(&moveProfile.relativeMove, 0, sizeof(moveProfile.relativeMove));
    moveProfile.relativeMove.ena = false;
    moveProfile.relativeMove.relativeOrientation.orientation.w=1;
    moveProfile.relativeMove.relativeOrientation.orientation.x=0;
    moveProfile.relativeMove.relativeOrientation.orientation.y=0;
    moveProfile.relativeMove.relativeOrientation.orientation.z=0;

    //工具参数
    moveProfile.toolInEndPosition.position.x = s_moveProfileToolInEndDesc.toolInEndPosition.x;
    moveProfile.toolInEndPosition.position.y = s_moveProfileToolInEndDesc.toolInEndPosition.y;
    moveProfile.toolInEndPosition.position.z = s_moveProfileToolInEndDesc.toolInEndPosition.z;
    moveProfile.toolInEndOrientation.w = s_moveProfileToolInEndDesc.toolInEndOrientation.w;
    moveProfile.toolInEndOrientation.x = s_moveProfileToolInEndDesc.toolInEndOrientation.x;
    moveProfile.toolInEndOrientation.y = s_moveProfileToolInEndDesc.toolInEndOrientation.y;
    moveProfile.toolInEndOrientation.z = s_moveProfileToolInEndDesc.toolInEndOrientation.z;

    if(moveMode==MODEP && subMoveMode==JOINT_UBSPLINEINTP)
    {
        moveProfile.blendRadius = s_moveProfileTrackPlaybackCycle;
    }

    if(moveMode==MODEP)
    {
        switch(subMoveMode)
        {
        case ARC:
            moveProfile.blendRadius = 1;
            moveProfile.circularLoopTimes=0;
            moveProfile.subMoveMode = ARC_CIR;
            break;
        case CIRCLE:
            moveProfile.blendRadius = 1;
            moveProfile.subMoveMode = ARC_CIR;
            break;
        case ARC_ORI_ROTATED:
            moveProfile.blendRadius = -1.0;
            moveProfile.circularLoopTimes=0;
            moveProfile.subMoveMode = ARC_CIR;
            break;
        case CIRCLE_ORI_ROTATED:
            moveProfile.blendRadius = -1.0;
            moveProfile.circularLoopTimes=1;
            moveProfile.subMoveMode = ARC_CIR;
            break;
        case ORI_POSITION_ROTATE_CIRCUMFERENCE:
            moveProfile.blendRadius = -1.0;
            moveProfile.circularLoopTimes=1;
            moveProfile.subMoveMode = ARC_CIR;
            break;
        default:
            break;
        }
    }

    //关于提前到位
    moveProfile.arrivalAhead.arrivalAheadStat = s_moveArrivalAhead.arrivalAheadStat;
    moveProfile.arrivalAhead.arrivalAheadThr  = s_moveArrivalAhead.arrivalAheadThr;
    if((moveMode==MODEJ || moveMode==MODEL) &&
       (moveProfile.arrivalAhead.arrivalAheadStat == arrival_ahead_distance || moveProfile.arrivalAhead.arrivalAheadStat == arrival_ahead_time)  )
    {
        //关节运动&&提前到位(时间或距离)模式下设置为跟随模式
        moveProfile.subMoveMode = TRACKING;
    }

    //设置速度加速度
    moveProfile.jerkAccRatio = s_moveProfileJerkAccRatio;
    if( checkIsJointTypeTrack(moveProfile.moveMode, moveProfile.subMoveMode) )   //关节型运动轨迹
    {
        for(int i=0; i<ARM_DOF; i++)
        {
            moveProfile.maxAcc.jointPara[i]  = s_moveProfileJointMaxAcc.jointPara[i];
            moveProfile.maxVelc.jointPara[i] = s_moveProfileJointMaxVelc.jointPara[i];
        }
    }
    else   //末端型运动轨迹
    {
        moveProfile.maxAcc.cartPara[0]  = s_moveProfileEndMaxLineAcc;
        moveProfile.maxVelc.cartPara[0] = s_moveProfileEndMaxLineVelc;

        moveProfile.maxAcc.cartPara[1]  = s_moveProfileEndMaxAngleAcc;
        moveProfile.maxVelc.cartPara[1] = s_moveProfileEndMaxAngleVelc;
    }
}


int RobotMoveService::modifyOffsetProfileByWaypointAndGlobalOffset(wayPoint_S offset_reference_waypoint, RobotMoveProfile &moveProfile)
{
    int ret = ErrnoSucc;

    //偏移量属性  MoveRelativeParam_t+waypoint=RobotMoveProfile.relativeMove
    moveProfile.relativeMove.ena = s_moveProfileRelativeParam.isEnable;

    if(moveProfile.relativeMove.ena==true)
    {
        if(s_moveProfileRelativeParam.coordinateSystem.coordType==BaseCoordinate)
        {
            moveProfile.relativeMove.relativeOrientation.orientation.w=s_moveProfileRelativeParam.relativeOrientation.orientation.w;
            moveProfile.relativeMove.relativeOrientation.orientation.x=s_moveProfileRelativeParam.relativeOrientation.orientation.x;
            moveProfile.relativeMove.relativeOrientation.orientation.y=s_moveProfileRelativeParam.relativeOrientation.orientation.y;
            moveProfile.relativeMove.relativeOrientation.orientation.z=s_moveProfileRelativeParam.relativeOrientation.orientation.z;

            for(int i=0;i<3;i++)
            {
                moveProfile.relativeMove.relativePosition[i]=s_moveProfileRelativeParam.relativePosition[i];
            }
        }
        else
        {
           aubo_robot_namespace::Ori sourceRelativeOriOnUser;
           aubo_robot_namespace::Pos sourceRelativePositionOnUser;

           aubo_robot_namespace::Ori targetOriOffsetOnBase;
           aubo_robot_namespace::Pos targetPositionOffsetOnBase;

           bool relativeMoveAndRotResult = true;

           sourceRelativePositionOnUser.x = s_moveProfileRelativeParam.relativePosition[0];
           sourceRelativePositionOnUser.y = s_moveProfileRelativeParam.relativePosition[1];
           sourceRelativePositionOnUser.z = s_moveProfileRelativeParam.relativePosition[2];
           sourceRelativeOriOnUser        = s_moveProfileRelativeParam.relativeOrientation.orientation;


           relativeMoveAndRotResult = RobotUtilService::relativeMoveAndRot(offset_reference_waypoint,
                                                     s_moveProfileRelativeParam.coordinateSystem,
                                                     s_moveProfileToolInEndDesc,
                                                     sourceRelativePositionOnUser,
                                                     sourceRelativeOriOnUser,
                                                     targetPositionOffsetOnBase,
                                                     targetOriOffsetOnBase);

           if(relativeMoveAndRotResult == true)
           {

               moveProfile.relativeMove.relativePosition[0]=targetPositionOffsetOnBase.x;
               moveProfile.relativeMove.relativePosition[1]=targetPositionOffsetOnBase.y;
               moveProfile.relativeMove.relativePosition[2]=targetPositionOffsetOnBase.z;

               moveProfile.relativeMove.relativeOrientation.orientation = targetOriOffsetOnBase;

               ret = ErrnoSucc;
           }
           else
           {
               moveProfile.relativeMove.ena=false;
               ret = ErrCode_Failed;
           }
        }
    }

    return ret;
}

int RobotMoveService::userCoordRelativeToBaseCoordRelative(wayPoint_S &offset_reference_waypoint, const MoveRelative &relativeOnUserCoord, const CoordCalibrateByJointAngleAndTool &userCoord, MoveRelative &relativeOnBase)
{
    int ret = ErrnoSucc;

    //偏移量属性  MoveRelativeParam_t+waypoint=RobotMoveProfile.relativeMove
    relativeOnBase.ena = relativeOnUserCoord.ena;

    if(relativeOnBase.ena == false)
    {
        relativeOnBase.relativePosition[0] = 0.0;
        relativeOnBase.relativePosition[1] = 0.0;
        relativeOnBase.relativePosition[2] = 0.0;

        relativeOnBase.relativeOri.w = 1.0;
        relativeOnBase.relativeOri.x = 0.0;
        relativeOnBase.relativeOri.y = 0.0;
        relativeOnBase.relativeOri.z = 0.0;

        return ret;
    }

    if(userCoord.coordType==BaseCoordinate)
    {
        relativeOnBase = relativeOnUserCoord;
    }
    else
    {
           aubo_robot_namespace::Ori sourceRelativeOriOnUser;
           aubo_robot_namespace::Pos sourceRelativePositionOnUser;

           aubo_robot_namespace::Ori targetOriOffsetOnBase;
           aubo_robot_namespace::Pos targetPositionOffsetOnBase;

           bool relativeMoveAndRotResult = true;

           sourceRelativePositionOnUser.x = relativeOnUserCoord.relativePosition[0];
           sourceRelativePositionOnUser.y = relativeOnUserCoord.relativePosition[1];
           sourceRelativePositionOnUser.z = relativeOnUserCoord.relativePosition[2];
           sourceRelativeOriOnUser        = relativeOnUserCoord.relativeOri;


           relativeMoveAndRotResult = RobotUtilService::relativeMoveAndRot(offset_reference_waypoint,
                                                     s_moveProfileRelativeParam.coordinateSystem,
                                                     s_moveProfileToolInEndDesc,
                                                     sourceRelativePositionOnUser,
                                                     sourceRelativeOriOnUser,
                                                     targetPositionOffsetOnBase,
                                                     targetOriOffsetOnBase);

           if(relativeMoveAndRotResult == true)
           {

               relativeOnBase.relativePosition[0]=targetPositionOffsetOnBase.x;
               relativeOnBase.relativePosition[1]=targetPositionOffsetOnBase.y;
               relativeOnBase.relativePosition[2]=targetPositionOffsetOnBase.z;

               relativeOnBase.relativeOri = targetOriOffsetOnBase;

               ret = ErrnoSucc;
           }
           else
           {
               relativeOnBase.ena = false;
               ret = ErrCode_Failed;
           }
    }

    return ret;
}



void RobotMoveService::MoveProfileConvert(const move_mode  moveMode, aubo_robot_namespace::wayPoint_S &offset_reference_waypoint,
                                                        aubo_robot_namespace::MoveProfile_t  &sourceMoveProfile, RobotMoveProfile &targetMoveProfile)
{
    initDefaultMoveProfile(targetMoveProfile);

    targetMoveProfile.moveMode     = moveMode;
    targetMoveProfile.subMoveMode  = NO_TRACK;
    targetMoveProfile.teachMode    = NO_TEACH;

    targetMoveProfile.enableIterIk = 0;
    targetMoveProfile.toolTrack    = 0;

    //工具参数
    targetMoveProfile.toolInEndPosition.position = sourceMoveProfile.toolInEndDesc.toolInEndPosition;
    targetMoveProfile.toolInEndOrientation       = sourceMoveProfile.toolInEndDesc.toolInEndOrientation;

    //偏移量属性
    MoveRelative relativeOnBase;
    userCoordRelativeToBaseCoordRelative(offset_reference_waypoint, sourceMoveProfile.relative, sourceMoveProfile.relativeOnCoord, relativeOnBase);
    targetMoveProfile.relativeMove.ena = relativeOnBase.ena;
    for(int i=0;i<3;i++)
        targetMoveProfile.relativeMove.relativePosition[i] = relativeOnBase.relativePosition[i];
    targetMoveProfile.relativeMove.relativeOrientation.orientation = relativeOnBase.relativeOri;

    //设置速度加速度
    if( checkIsJointTypeTrack(targetMoveProfile.moveMode, targetMoveProfile.subMoveMode) )
    {
        //关节型运动轨迹
        for(int i=0; i<ARM_DOF; i++)
        {
            targetMoveProfile.maxAcc.jointPara[i]  = sourceMoveProfile.jointMaxAcc[i];
            targetMoveProfile.maxVelc.jointPara[i] = sourceMoveProfile.jointMaxVelc[i];
        }
    }
    else
    {
        //末端型运动轨迹
        targetMoveProfile.maxAcc.cartPara[0]  = sourceMoveProfile.endMaxLineAcc;
        targetMoveProfile.maxVelc.cartPara[0] = sourceMoveProfile.endMaxLineVelc;

        targetMoveProfile.maxAcc.cartPara[1]  = DEFALT_END_ANGLE_ACC;
        targetMoveProfile.maxVelc.cartPara[1] = DEFALT_END_ANGLE_VELC;
    }

    targetMoveProfile.arrivalAhead.arrivalAheadStat = arrival_ahead_none;
    targetMoveProfile.arrivalAhead.arrivalAheadThr  = 0.0;

    targetMoveProfile.blendRadius = targetMoveProfile.blendRadius;
    targetMoveProfile.circularLoopTimes = 0;
}


void RobotMoveService::printWaypoint(wayPoint_S &wayPoint)
{
    std::cout<<"waypoint info:-------------------------"<<std::endl;
    std::cout<<"x:"<<wayPoint.cartPos.position.x<<", ";
    std::cout<<"y:"<<wayPoint.cartPos.position.y<<", ";
    std::cout<<"z:"<<wayPoint.cartPos.position.z<<std::endl;

    std::cout<<"w:"<<wayPoint.orientation.w<<", ";
    std::cout<<"x:"<<wayPoint.orientation.x<<", ";
    std::cout<<"y:"<<wayPoint.orientation.y<<", ";
    std::cout<<"z:"<<wayPoint.orientation.z<<std::endl;

    for(int i=0;i<ARM_DOF;i++)
    {
        std::cout<<"joint:"<<wayPoint.jointpos[i]<<", ";
    }
    std::cout<<"----------------------------------"<<std::endl;
}
