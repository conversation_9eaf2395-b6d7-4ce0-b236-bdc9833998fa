#include "protoencodedecode.h"
#include "globalutil.h"
#include "robotcontrolservices.h"

using namespace std;
using namespace aubo_robot_namespace;
using namespace aubo_robot_logtrace;


ProtoEncodeDecode::ProtoEncodeDecode()
{
}

//路点类型转换
bool ProtoEncodeDecode::roadPointTypeConversion(const aubo::robot::common::ProtoRoadPoint &protoRoadPoint, wayPoint_S &roadPoint)
{
    bool ret = true;

    if(protoRoadPoint.cartpos().position_size()>0)
    {
        roadPoint.cartPos.position.x = protoRoadPoint.cartpos().position(0).x();
        roadPoint.cartPos.position.y = protoRoadPoint.cartpos().position(0).y();
        roadPoint.cartPos.position.z = protoRoadPoint.cartpos().position(0).z();
    }
    else if(protoRoadPoint.cartpos().positionvector_size()>2)
    {
        roadPoint.cartPos.position.x = protoRoadPoint.cartpos().positionvector(0);
        roadPoint.cartPos.position.y = protoRoadPoint.cartpos().positionvector(0);
        roadPoint.cartPos.position.z = protoRoadPoint.cartpos().positionvector(0);
    }
    else
    {
        ret = false;
    }


    roadPoint.orientation.w = protoRoadPoint.orientation().w();
    roadPoint.orientation.x = protoRoadPoint.orientation().x();
    roadPoint.orientation.y = protoRoadPoint.orientation().y();
    roadPoint.orientation.z = protoRoadPoint.orientation().z();


    if(protoRoadPoint.jointpos_size() == ARM_DOF)
    {
        for(int i=0;i<ARM_DOF; i++)
        {
            roadPoint.jointpos[i] = protoRoadPoint.jointpos(i);
        }
    }
    else
    {
        ret = false;
    }

    return ret;
}


//路点类型转换
bool ProtoEncodeDecode::roadPointTypeConversion(const wayPoint_S &roadpoint, aubo::robot::common::ProtoRoadPoint &protoRoadPoint)
{
    bool ret = true;

    //TODO: cartesianPos_U
    aubo::robot::common::cartesianPos_U* tempCartesianOri= protoRoadPoint.mutable_cartpos();
    aubo::robot::common::Pos* tempPosition = tempCartesianOri->add_position();
    tempPosition->set_x(roadpoint.cartPos.position.x);
    tempPosition->set_y(roadpoint.cartPos.position.y);
    tempPosition->set_z(roadpoint.cartPos.position.z);

    tempCartesianOri->add_positionvector(roadpoint.cartPos.position.x);
    tempCartesianOri->add_positionvector(roadpoint.cartPos.position.y);
    tempCartesianOri->add_positionvector(roadpoint.cartPos.position.z);

    //TODO: Ori
    aubo::robot::common::Ori* tempOri = protoRoadPoint.mutable_orientation();
    tempOri->set_w(roadpoint.orientation.w);
    tempOri->set_x(roadpoint.orientation.x);
    tempOri->set_y(roadpoint.orientation.y);
    tempOri->set_z(roadpoint.orientation.z);

    //TODO: joint
    for(int index=0;index<ARM_DOF;index++)
    {
        protoRoadPoint.add_jointpos(roadpoint.jointpos[index]);
    }

    return ret;
}




bool ProtoEncodeDecode::jointCartUTypeConversion(const aubo::robot::common::joint_cart_U &src, joint_cart_U &target)
{
    bool ret = true;

//    target.cartPara = (src.cartpara_size()>0)? src.cartpara(0):0;

    memset(&target.jointPara[0], 0, sizeof(target.jointPara));

    for(int i=0;i<ARM_DOF&&i<src.jointpara_size();i++)
    {
        target.jointPara[i] = src.jointpara(i);
    }

    return ret;
}


bool ProtoEncodeDecode::jointCartUTypeConversion(const joint_cart_U  &src, aubo::robot::common::joint_cart_U &target)
{
    bool ret = true;

    //TODO
//    target.add_cartpara(src.cartPara);

    for(int i=0;i<ARM_DOF;i++)
    {
        target.add_jointpara(src.jointPara[i]);
    }

    return ret;
}


bool ProtoEncodeDecode::cartesianOriUTypeConversion(const aubo::robot::common::cartesianOri_U &src, cartesian_Ori_U &target)
{
    bool ret = true;

    if(src.orientation_size()>0)
    {
        target.orientation.w = src.orientation(0).w();
        target.orientation.x = src.orientation(0).x();
        target.orientation.y = src.orientation(0).y();
        target.orientation.z = src.orientation(0).z();
    }

    for(int i=0;i<4&&i<src.quaternionvector_size();i++)
    {
        target.quaternionVector[i] = src.quaternionvector(i);
    }

    return ret;
}


bool ProtoEncodeDecode::cartesianOriUTypeConversion(const cartesian_Ori_U &src, aubo::robot::common::cartesianOri_U &target)
{
    bool ret = true;

    aubo::robot::common::Ori * protoOri = target.add_orientation();
    protoOri->set_w(src.orientation.w);
    protoOri->set_x(src.orientation.x);
    protoOri->set_y(src.orientation.y);
    protoOri->set_z(src.orientation.z);

    target.add_quaternionvector(src.orientation.w);
    target.add_quaternionvector(src.orientation.x);
    target.add_quaternionvector(src.orientation.y);
    target.add_quaternionvector(src.orientation.z);

    return ret;
}


bool ProtoEncodeDecode::RobotMovePatamTypeConversion(const aubo::robot::common::RobotMove &protoRobotMove, RobotMoveProfile &moveProfile, std::vector<wayPoint_S> &roadpointVector)
{
    bool ret = true;

    ProtoRobotMoveProfileType2AuboType(protoRobotMove.move_profile(), moveProfile);

    roadpointVector.clear();
    for(int i=0;i<protoRobotMove.roadpointvector_size();i++)
    {
        wayPoint_S temp;
        roadPointTypeConversion(protoRobotMove.roadpointvector(i), temp);

        roadpointVector.push_back(temp);
    }

    return ret;
}


bool ProtoEncodeDecode::RobotMovePatamTypeConversion(const RobotMoveProfile &moveProfile, const std::vector<wayPoint_S> &roadpointVector, aubo::robot::common::RobotMove &target)
{
    bool ret = true;

    AuboRobotMoveProfileType2ProtoType(moveProfile, *(target.mutable_move_profile()));

    //TODO: roadpointVector
    for(size_t i=0;i<roadpointVector.size();i++)
    {
        const wayPoint_S &roadpoint = roadpointVector[i];

        aubo::robot::common::ProtoRoadPoint *temp = target.add_roadpointvector();

        roadPointTypeConversion(roadpoint, *temp);
    }

    return ret;
}

void ProtoEncodeDecode::AuboRobotMoveProfileType2ProtoType(const RobotMoveProfile &moveProfile, aubo::robot::common::RobotMoveProfile &target)
{
    target.set_movemode((aubo::robot::movecondition::move_mode)moveProfile.moveMode);
    target.set_submovemode((aubo::robot::movecondition::move_track)moveProfile.subMoveMode);
    target.set_teachmode((aubo::robot::movecondition::teach_mode)moveProfile.teachMode);
    target.set_enableiterik(moveProfile.enableIterIk);
    target.set_tooltrack(moveProfile.toolTrack);

    //tool
    aubo::robot::common::Pos* pToolInEndPosition = target.mutable_toolinendposition();
    pToolInEndPosition->set_x(moveProfile.toolInEndPosition.position.x);
    pToolInEndPosition->set_y(moveProfile.toolInEndPosition.position.y);
    pToolInEndPosition->set_z(moveProfile.toolInEndPosition.position.z);
    aubo::robot::common::Ori* pToolInEndOrientation = target.mutable_toolinendorientation();
    pToolInEndOrientation->set_w(moveProfile.toolInEndOrientation.w);
    pToolInEndOrientation->set_x(moveProfile.toolInEndOrientation.x);
    pToolInEndOrientation->set_y(moveProfile.toolInEndOrientation.y);
    pToolInEndOrientation->set_z(moveProfile.toolInEndOrientation.z);

    //TODO:relativeMove_t
    aubo::robot::common::relativeMove_t *protoRelativemove = target.mutable_relativemove();

    protoRelativemove->set_ena(moveProfile.relativeMove.ena);
    protoRelativemove->add_relativeposition(moveProfile.relativeMove.relativePosition[0]);
    protoRelativemove->add_relativeposition(moveProfile.relativeMove.relativePosition[1]);
    protoRelativemove->add_relativeposition(moveProfile.relativeMove.relativePosition[2]);

    cartesianOriUTypeConversion(moveProfile.relativeMove.relativeOrientation,*protoRelativemove->mutable_relativeorientation());

    //TODO:joint_cart_U  maxVelc
    jointCartUTypeConversion(moveProfile.maxVelc, *(target.mutable_maxvelc()));

    //TODO:joint_cart_U maxAcc
    jointCartUTypeConversion(moveProfile.maxAcc, *(target.mutable_maxacc()));

    target.set_blendradius(moveProfile.blendRadius);
    target.set_circularlooptimes(moveProfile.circularLoopTimes);

    aubo::robot::common::arrivalAhead_t* protoArrivalAhead = target.mutable_arrivalahead();
    protoArrivalAhead->set_arrivalaheadstat(moveProfile.arrivalAhead.arrivalAheadStat);
    protoArrivalAhead->set_arrivalaheadthr(moveProfile.arrivalAhead.arrivalAheadThr);

//    target.set_jerkaccratio(moveProfile.jerkAccRatio);
}

void ProtoEncodeDecode::ProtoRobotMoveProfileType2AuboType(const aubo::robot::common::RobotMoveProfile &ProtoMoveProfile, RobotMoveProfile &target)
{
    target.moveMode    = (move_mode)ProtoMoveProfile.movemode();
    target.subMoveMode = (move_track)ProtoMoveProfile.submovemode();
    target.teachMode   = (teach_mode)ProtoMoveProfile.teachmode();
    target.enableIterIk= ProtoMoveProfile.enableiterik();
    target.toolTrack   = ProtoMoveProfile.tooltrack();

    target.toolInEndPosition.position.x = ProtoMoveProfile.toolinendposition().x();
    target.toolInEndPosition.position.y = ProtoMoveProfile.toolinendposition().y();
    target.toolInEndPosition.position.z = ProtoMoveProfile.toolinendposition().z();
    target.toolInEndOrientation.w = ProtoMoveProfile.toolinendorientation().w();
    target.toolInEndOrientation.x = ProtoMoveProfile.toolinendorientation().x();
    target.toolInEndOrientation.y = ProtoMoveProfile.toolinendorientation().y();
    target.toolInEndOrientation.z = ProtoMoveProfile.toolinendorientation().z();

    //relativeMove
    target.relativeMove.ena = ProtoMoveProfile.relativemove().ena();
    if(ProtoMoveProfile.relativemove().relativeposition_size() == 3)
    {
        target.relativeMove.relativePosition[0] = ProtoMoveProfile.relativemove().relativeposition(0);
        target.relativeMove.relativePosition[1] = ProtoMoveProfile.relativemove().relativeposition(1);
        target.relativeMove.relativePosition[2] = ProtoMoveProfile.relativemove().relativeposition(2);
    }
    cartesianOriUTypeConversion(ProtoMoveProfile.relativemove().relativeorientation(), target.relativeMove.relativeOrientation);

    //TODO: joint_cart_U  maxVelc
    jointCartUTypeConversion(ProtoMoveProfile.maxvelc(), target.maxVelc);

    //TODO: joint_cart_U  maxAcc
    jointCartUTypeConversion(ProtoMoveProfile.maxacc(), target.maxAcc);

    target.blendRadius = ProtoMoveProfile.blendradius();
    target.circularLoopTimes = ProtoMoveProfile.circularlooptimes();

    target.arrivalAhead.arrivalAheadStat = (arrival_ahead_state)ProtoMoveProfile.arrivalahead().arrivalaheadstat();
    target.arrivalAhead.arrivalAheadThr  = ProtoMoveProfile.arrivalahead().arrivalaheadthr();

//    target.jerkAccRatio = ProtoMoveProfile.jerkaccratio();
}


bool ProtoEncodeDecode::robotTcpParamTypeConversion(const RobotTcpParam &src, aubo::robot::communication::RobotTcpParam &target)
{
    bool ret = true;

    aubo::robot::communication::TcpParam* tcpParamAuto = target.mutable_paramautorun();
    tcpParamAuto->set_positionx(src.paramAutorun.positionX);
    tcpParamAuto->set_positiony(src.paramAutorun.positionY);
    tcpParamAuto->set_positionz(src.paramAutorun.positionZ);
    tcpParamAuto->set_payload(src.paramAutorun.payload);

    aubo::robot::communication::TcpParam* tcpParamManual = target.mutable_parammanual();
    tcpParamManual->set_positionx(src.paramManual.positionX);
    tcpParamManual->set_positiony(src.paramManual.positionY);
    tcpParamManual->set_positionz(src.paramManual.positionZ);
    tcpParamManual->set_payload(src.paramManual.payload);

    return ret;
}


bool ProtoEncodeDecode::robotTcpParamTypeConversion(const aubo::robot::communication::RobotTcpParam &src,  RobotTcpParam&target)
{
    bool ret = true;

    target.paramAutorun.positionX = src.paramautorun().positionx();
    target.paramAutorun.positionY = src.paramautorun().positiony();
    target.paramAutorun.positionZ = src.paramautorun().positionz();
    target.paramAutorun.payload   = src.paramautorun().payload();

    target.paramManual.positionX  = src.parammanual().positionx();
    target.paramManual.positionY  = src.parammanual().positiony();
    target.paramManual.positionZ  = src.parammanual().positionz();
    target.paramManual.payload    = src.parammanual().payload();

    return ret;
}

bool ProtoEncodeDecode::jointStatusTypeConversion(const JointStatus &src,
                                                     aubo::robot::common::ProtoJointStatus &target)
{
    bool ret = true;

    target.set_jointcurrenti(src.jointCurrentI);
    target.set_jointspeedmoto(src.jointSpeedMoto);
    target.set_jointposj(src.jointPosJ);
    target.set_jointcurvol(src.jointCurVol);
    target.set_jointcurtemp(src.jointCurTemp);
    target.set_jointtagcurrenti(src.jointTagCurrentI);
    target.set_jointtagspeedmoto(src.jointTagSpeedMoto);
    target.set_jointtagposj(src.jointTagPosJ);
    target.set_jointerrornum(src.jointErrorNum);

    return ret;
}

bool ProtoEncodeDecode::jointStatusTypeConversion(const aubo::robot::common::ProtoJointStatus &src, JointStatus &target)
{
    bool ret = true;

    target.jointCurrentI  = src.jointcurrenti();
    target.jointSpeedMoto = src.jointspeedmoto();
    target.jointPosJ      = src.jointposj();
    target.jointCurVol    = src.jointcurvol();
    target.jointCurTemp   = src.jointcurtemp();
    target.jointTagCurrentI = src.jointtagcurrenti();
    target.jointTagSpeedMoto= src.jointtagspeedmoto();
    target.jointTagPosJ   = src.jointtagposj();
    target.jointErrorNum  = src.jointerrornum();

    return ret;
}





bool ProtoEncodeDecode::robotDiagnosisTypeConversionToProto(const RobotDiagnosis &src, aubo::robot::communication::RobotDiagnosis &target)
{
    bool ret = true;

    target.set_armcanbusstatus(src.armCanbusStatus);                     //CAN通信状态
    target.set_armpowercurrent(src.armPowerCurrent);                     //机械臂48V电源当前电流
    target.set_armpowervoltage(src.armPowerVoltage);                     //机械臂48V电源当前电压
    target.set_armpowerstatus(src.armPowerStatus);                       //机械臂48V电源状态（开、关）
    target.set_contorllertemp(src.contorllerTemp);                       //控制箱温度
    target.set_contorllerhumidity(src.contorllerHumidity);               //控制箱湿度
    target.set_remotehalt(src.remoteHalt);                               //远程关机信号
    target.set_softemergency(src.softEmergency);                         //机械臂软急停
    target.set_remoteemergency(src.remoteEmergency);                     //远程急停信号
    target.set_robotcollision(src.robotCollision);                       //碰撞检测位
    target.set_forcecontrolmode(src.forceControlMode);                   //机械臂进入力控模式标志位
    target.set_brakestuats(src.brakeStuats);                             //刹车状态
    target.set_robotendspeed(src.robotEndSpeed);                         //末端速度
    target.set_robotmaxacc(src.robotMaxAcc);                             //最大加速度
    target.set_orpestatus(src.orpeStatus);                               //上位机软件状态位
    target.set_enablereadpose(src.enableReadPose);                       //位姿读取使能位
    target.set_robotmountingposechanged(src.robotMountingPoseChanged);   //安装位置状态
    target.set_encodererrorstatus(src.encoderErrorStatus);               //磁编码器错误状态
    target.set_staticcollisiondetect(src.staticCollisionDetect);         //静止碰撞检测开关
    target.set_jointcollisiondetect(src.jointCollisionDetect);           //关节碰撞检测 每个关节占用1bit 0-无碰撞 1-存在碰撞
    target.set_encoderlineserror(src.encoderLinesError);                 //光电编码器不一致错误 0-无错误 1-有错误
    target.set_jointerrorstatus(src.jointErrorStatus);                   //joint error status
    target.set_singularityoverspeedalarm(src.singularityOverSpeedAlarm); //机械臂奇异点过速警告
    target.set_robotcurrentalarm(src.robotCurrentAlarm);                 //机械臂电流错误警告
    target.set_toolioerror(src.toolIoError);
    target.set_robotmountingposewarning(src.robotMountingPoseWarning);   //机械臂安装位置错位（只在力控模式下起作用）
    target.set_mactargetposbuffersize(src.macTargetPosBufferSize);       //mac缓冲器长度
    target.set_mactargetposdatasize(src.macTargetPosDataSize);           //mac缓冲器有效数据长度
    target.set_macdatainterruptwarning(src.macDataInterruptWarning);     //mac数据中断
    target.set_controlboardabnormalstateflag(src.controlBoardAbnormalStateFlag); //主控板(接口板)异常状态标志
    return  ret;
}


bool ProtoEncodeDecode::robotDiagnosisTypeConversionToStruct(const aubo::robot::communication::RobotDiagnosis &src,  RobotDiagnosis &target)
{
    bool ret = true;

    target.armCanbusStatus  = src.armcanbusstatus();
    target.armPowerCurrent  = src.armpowercurrent();
    target.armPowerVoltage  = src.armpowervoltage();
    target.armPowerStatus   = src.armpowerstatus();
    target.contorllerTemp   = src.contorllertemp();
    target.contorllerHumidity = src.contorllerhumidity();
    target.remoteHalt       = src.remotehalt();
    target.softEmergency    = src.softemergency();
    target.remoteEmergency  = src.remoteemergency();
    target.robotCollision   = src.robotcollision();
    target.forceControlMode = src.forcecontrolmode();
    target.brakeStuats      = src.brakestuats();
    target.robotEndSpeed    = src.robotendspeed();
    target.robotMaxAcc      = src.robotmaxacc();
    target.orpeStatus       = src.orpestatus();
    target.enableReadPose   = src.enablereadpose();
    target.robotMountingPoseChanged = src.robotmountingposechanged();
    target.encoderErrorStatus   = src.encodererrorstatus();
    target.staticCollisionDetect= src.staticcollisiondetect();
    target.jointCollisionDetect = src.jointcollisiondetect();
    target.encoderLinesError    = src.encoderlineserror();
    target.jointErrorStatus     = src.jointerrorstatus();
    target.singularityOverSpeedAlarm = src.singularityoverspeedalarm();
    target.robotCurrentAlarm    = src.robotcurrentalarm();
    target.toolIoError          = src.toolioerror();
    target.robotMountingPoseWarning = src.robotmountingposewarning();
    target.macTargetPosBufferSize   = src.mactargetposbuffersize();
    target.macTargetPosDataSize     = src.mactargetposdatasize();
    target.macDataInterruptWarning  = src.macdatainterruptwarning();
    target.controlBoardAbnormalStateFlag = src.controlboardabnormalstateflag();
    return  ret;
}


bool ProtoEncodeDecode::robotGravityComponentTypeConversion(const RobotGravityComponent &src, aubo::robot::communication::RobotGravityComponent &target)
{
    bool ret = true;

    target.set_x(src.x);
    target.set_y(src.y);
    target.set_z(src.z);

    return  ret;
}


bool ProtoEncodeDecode::robotGravityComponentTypeConversion(const aubo::robot::communication::RobotGravityComponent &src, RobotGravityComponent &target)
{
    bool ret = true;

    target.x = src.x();
    target.y = src.y();
    target.z = src.z();

    return  ret;
}


bool ProtoEncodeDecode::robotCollisionCurrentTypeConversion(const RobotCollisionCurrent &src, aubo::robot::communication::RobotCollisionCurrent &target)
{
    bool ret = true;

    target.set_collisionclass(src.CollisionClass);

    return  ret;
}


bool ProtoEncodeDecode::robotCollisionCurrentTypeConversion(const aubo::robot::communication::RobotCollisionCurrent &src, RobotCollisionCurrent &target)
{
    bool ret = true;

    target.CollisionClass = src.collisionclass();

    return  ret;
}


//bool ProtoEncodeDecode::robotCollisionCurrentTypeConversion(const aubo::robot::communication::OurRobotDevInfo &src, OurRobotDevInfo &target)
//{
//    bool ret = true;

//    //TODO ...
//    return  ret;
//}


//bool ProtoEncodeDecode::robotCollisionCurrentTypeConversion(const OurRobotDevInfo &src, aubo::robot::communication::OurRobotDevInfo &target)
//{
//    bool ret = true;

//    //TODO ...
//    return  ret;
//}



bool ProtoEncodeDecode::RobotSafetyConfigTypeConversion(const RobotSafetyConfig &src, aubo::robot::common::ProtoRobotSafetyConfig &target)
{
    bool ret = true;

    for(int i=0;i<6;i++)
    {
        target.add_robotreducedconfigjointspeed(src.robotReducedConfigJointSpeed[i]);
    }
    target.set_robotreducedconfigtcpspeed(src.robotReducedConfigTcpSpeed);
    target.set_robotreducedconfigtcpforce(src.robotReducedConfigTcpForce);
    target.set_robotreducedconfigmomentum(src.robotReducedConfigMomentum);
    target.set_robotreducedconfigpower(src.robotReducedConfigPower);
    target.set_robotsafeguradresetconfig(src.robotSafeguradResetConfig);
    target.set_robotoperationalmodeconfig(src.robotOperationalModeConfig);

    return  ret;
}

bool ProtoEncodeDecode::RobotSafetyConfigTypeConversion(const aubo::robot::common::ProtoRobotSafetyConfig &src, RobotSafetyConfig &target)
{
    bool ret = true;

    for(int i=0;i<6&&i<src.robotreducedconfigjointspeed_size();i++)
    {
        target.robotReducedConfigJointSpeed[i] = src.robotreducedconfigjointspeed(i);
    }

    target.robotReducedConfigTcpSpeed = src.robotreducedconfigtcpspeed();
    target.robotReducedConfigTcpForce = src.robotreducedconfigtcpforce();
    target.robotReducedConfigMomentum = src.robotreducedconfigmomentum();
    target.robotReducedConfigPower = src.robotreducedconfigpower();
    target.robotSafeguradResetConfig = src.robotsafeguradresetconfig();
    target.robotOperationalModeConfig = src.robotoperationalmodeconfig();

    return  ret;
}

bool ProtoEncodeDecode::OrpeSafetyStatusTypeConversion(const OrpeSafetyStatus &src, aubo::robot::common::ProtoOrpeSafetyStatus &target)
{
    bool ret = true;

    target.set_orpepause((int32)src.orpePause);          //上位机暂停状态
    target.set_orpestop((int32)src.orpeStop);            //上位机停止状态

    for(int i=0;i<16;i++)
    {
        target.add_orpeerror((int32)src.orpeError[i]);   //上位机错误    orpeError[16]
    }

    target.set_systememergencystop((int32)src.systemEmergencyStop);
    target.set_reducedmodeerror((int32)src.reducedModeError);
    target.set_safetyguardresetsucc((int32)src.safetyguardResetSucc);

    return  ret;
}

bool ProtoEncodeDecode::OrpeSafetyStatusTypeConversion(const aubo::robot::common::ProtoOrpeSafetyStatus &src, OrpeSafetyStatus &target)
{
    bool ret = true;

    target.orpePause = (int8)src.orpepause();
    target.orpeStop  = (int8)src.orpestop();

    for(int i=0;i<16&&i<src.orpeerror_size();i++)
    {
        target.orpeError[i] = (uint8)src.orpeerror(i);
    }

    target.systemEmergencyStop = (uint8)src.systememergencystop();
    target.reducedModeError = (uint8)src.reducedmodeerror();
    target.safetyguardResetSucc = (uint8)src.safetyguardresetsucc();

    return  ret;
}


bool ProtoEncodeDecode::toolParamTypeConversion(const aubo::robot::common::ToolParam &protoToolParam, ToolDynamicsParam &toolDynamicsParam)
{
    bool ret = false;

    if(protoToolParam.tooldynamicsparam_size() > 0)
    {
        toolDynamicsParam.payload   = protoToolParam.tooldynamicsparam(0).payload();
        toolDynamicsParam.positionX = protoToolParam.tooldynamicsparam(0).positionx();
        toolDynamicsParam.positionY = protoToolParam.tooldynamicsparam(0).positiony();
        toolDynamicsParam.positionZ = protoToolParam.tooldynamicsparam(0).positionz();
        toolDynamicsParam.toolInertia.xx = protoToolParam.tooldynamicsparam(0).toolinertia().xx();
        toolDynamicsParam.toolInertia.xy = protoToolParam.tooldynamicsparam(0).toolinertia().xy();
        toolDynamicsParam.toolInertia.xz = protoToolParam.tooldynamicsparam(0).toolinertia().xz();
        toolDynamicsParam.toolInertia.yy = protoToolParam.tooldynamicsparam(0).toolinertia().yy();
        toolDynamicsParam.toolInertia.yz = protoToolParam.tooldynamicsparam(0).toolinertia().yz();
        toolDynamicsParam.toolInertia.zz = protoToolParam.tooldynamicsparam(0).toolinertia().zz();

        ret = true;
    }

    return ret;
}

bool ProtoEncodeDecode::toolParamTypeConversion(const aubo::robot::common::ToolParam &protoToolParam, ToolKinematicsParam &toolKinematicsParam)
{
    bool ret = false;

    if(protoToolParam.toolkinematicsparam_size() > 0)
    {
        toolKinematicsParam.toolInEndPosition.x = protoToolParam.toolkinematicsparam(0).toolposition().x();
        toolKinematicsParam.toolInEndPosition.y = protoToolParam.toolkinematicsparam(0).toolposition().y();
        toolKinematicsParam.toolInEndPosition.z = protoToolParam.toolkinematicsparam(0).toolposition().z();

        toolKinematicsParam.toolInEndOrientation.w = protoToolParam.toolkinematicsparam(0).toolori().w();
        toolKinematicsParam.toolInEndOrientation.x = protoToolParam.toolkinematicsparam(0).toolori().x();
        toolKinematicsParam.toolInEndOrientation.y = protoToolParam.toolkinematicsparam(0).toolori().y();
        toolKinematicsParam.toolInEndOrientation.z = protoToolParam.toolkinematicsparam(0).toolori().z();

        ret = true;
    }

    return ret;
}

bool ProtoEncodeDecode::toolParamTypeConversion(const ToolDynamicsParam &toolDynamicsParam, aubo::robot::common::ToolParam &protoToolParam)
{
    protoToolParam.clear_errorinfo();
    protoToolParam.clear_tooldynamicsparam();
    protoToolParam.clear_toolkinematicsparam();

    aubo::robot::common::ToolDynamicsParam* pProtoToolDynamicsParam = protoToolParam.add_tooldynamicsparam();

    pProtoToolDynamicsParam->set_payload(toolDynamicsParam.payload);
    pProtoToolDynamicsParam->set_positionx(toolDynamicsParam.positionX);
    pProtoToolDynamicsParam->set_positiony(toolDynamicsParam.positionY);
    pProtoToolDynamicsParam->set_positionz(toolDynamicsParam.positionZ);

    aubo::robot::common::ToolInertia* pProtoToolInertia = pProtoToolDynamicsParam->mutable_toolinertia();
    pProtoToolInertia->set_xx(toolDynamicsParam.toolInertia.xx);
    pProtoToolInertia->set_xy(toolDynamicsParam.toolInertia.xy);
    pProtoToolInertia->set_xz(toolDynamicsParam.toolInertia.xz);
    pProtoToolInertia->set_yy(toolDynamicsParam.toolInertia.yy);
    pProtoToolInertia->set_yz(toolDynamicsParam.toolInertia.yz);
    pProtoToolInertia->set_zz(toolDynamicsParam.toolInertia.zz);

    return true;
}


bool ProtoEncodeDecode::toolParamTypeConversion(const ToolKinematicsParam &toolKinematicsParam, aubo::robot::common::ToolParam &protoToolParam)
{
//    protoToolParam.clear_errorinfo();
//    protoToolParam.clear_tooldynamicsparam();
//    protoToolParam.clear_toolkinematicsparam();

    aubo::robot::common::ToolKinematicsParam* pProtoToolKinematicsParam = protoToolParam.add_toolkinematicsparam();

    aubo::robot::common::Pos* pProtoToolposition =pProtoToolKinematicsParam->mutable_toolposition();
    pProtoToolposition->set_x(toolKinematicsParam.toolInEndPosition.x);
    pProtoToolposition->set_y(toolKinematicsParam.toolInEndPosition.y);
    pProtoToolposition->set_z(toolKinematicsParam.toolInEndPosition.z);

    aubo::robot::common::Ori* pProtoToolOri =pProtoToolKinematicsParam->mutable_toolori();
    pProtoToolOri->set_w(toolKinematicsParam.toolInEndOrientation.w);
    pProtoToolOri->set_x(toolKinematicsParam.toolInEndOrientation.x);
    pProtoToolOri->set_y(toolKinematicsParam.toolInEndOrientation.y);
    pProtoToolOri->set_z(toolKinematicsParam.toolInEndOrientation.z);

    return true;
}



void ProtoEncodeDecode::robotEventTypeConversion(const RobotEventInfo &src, aubo::robot::communication::ProtoCommunicationRobotEvent &target)
{
    target.set_eventtype   (src.eventType);
    target.set_eventcode   (src.eventCode);
    target.set_eventcontent(src.eventContent);
}


void ProtoEncodeDecode::robotEventTypeConversion(const aubo::robot::communication::ProtoCommunicationRobotEvent &src, RobotEventInfo &target)
{
    (void)src;
    (void)target;

    target.eventType    = (RobotEventType)src.eventtype();
    target.eventCode    = src.eventcode();
    target.eventContent = src.eventcontent();
}


void ProtoEncodeDecode::RobotDevInfoTypeConversion(const aubo::robot::communication::OurRobotDevInfo &src, RobotDevInfo &target)
{
    memset(&target, 0, sizeof(target));

    target.type = (uint8)src.type();

    //revision
    memcpy(target.revision, src.revision().c_str(), (strlen(src.revision().c_str())>15)? 15:strlen(src.revision().c_str()) );

    //manu_id
    memcpy(target.manu_id,  src.manuid().c_str(),   (strlen(src.manuid().c_str())>15)?   15:strlen(src.manuid().c_str()) );

    //joint_type
    memcpy(target.joint_type, src.jointtype().c_str(), (strlen(src.jointtype().c_str())>15)? 15:strlen(src.jointtype().c_str()) );

    for(int i=0;i<8&&i<src.jointver_size();i++)
    {
        memcpy(target.joint_ver[i].sw_version, src.jointver(i).swversion().c_str(), (strlen(src.jointver(i).swversion().c_str())>15)? 15:strlen(src.jointver(i).swversion().c_str()) );
        memcpy(target.joint_ver[i].hw_version, src.jointver(i).hwversion().c_str(), (strlen(src.jointver(i).hwversion().c_str())>7)? 7:strlen(src.jointver(i).hwversion().c_str()) );
    }

    for(int i=0;i<8&&i<src.jointproductid_size();i++)
    {
        memcpy(target.jointProductID[i].productID, src.jointproductid(i).c_str(), (strlen(src.jointproductid(i).c_str())>15)? 15:strlen(src.jointproductid(i).c_str()) );
    }

    memcpy(target.desc, src.desc().c_str(), (strlen(src.desc().c_str())>11)? 11:strlen(src.desc().c_str()) );

    memcpy(target.slave_version, src.slavedevversion().c_str(), (strlen(src.slavedevversion().c_str())>15)? 15:strlen(src.slavedevversion().c_str()) );

    memcpy(target.extio_version, src.extendioboardversion().c_str(), (strlen(src.extendioboardversion().c_str())>11)? 11:strlen(src.extendioboardversion().c_str()) );
}





void ProtoEncodeDecode::RobotDiagnosisIODescTypeConversion(const RobotDiagnosisIODesc &src, aubo::robot::communication::ProtoRobotDiagnosisIODesc &target)
{
    target.set_addr(src.addr);
    target.set_value(src.value);
    target.set_type(src.type);
}


void ProtoEncodeDecode::RobotDiagnosisIODescTypeConversion(const aubo::robot::communication::ProtoRobotDiagnosisIODesc &src, RobotDiagnosisIODesc &target)
{
    target.addr  = src.addr();
    target.value = src.value();
    target.type  = src.type();
}


void ProtoEncodeDecode::RobotAnalogIODescTypeConversion(const RobotAnalogIODesc &src,     aubo::robot::communication::ProtoRobotAnalogIODesc &target)
{
    target.set_type (src.type);
    target.set_addr (src.addr);
    target.set_value(src.value);
}


void ProtoEncodeDecode::RobotAnalogIODescTypeConversion(const aubo::robot::communication::ProtoRobotAnalogIODesc &src,    RobotAnalogIODesc &target)
{
    target.addr  = src.addr();
    target.value = src.value();
    target.type  = src.type();
}

bool ProtoEncodeDecode::ProtoSeamTrackToSeamTrack(const aubo::robot::common::ProtoSeamTrack_t &protoSeamTrack, SeamTracking &target)
{
    target.trackEnable = (protoSeamTrack.trackenable()>0)? true:false;

    roadPointTypeConversion(protoSeamTrack.currentroadpoint(), target.currentRoadPoint);

    roadPointTypeConversion(protoSeamTrack.nextroadpoint(), target.nextRoadPoint);

    target.timeInterval = protoSeamTrack.timeinterval();

    for(int i=0;i<protoSeamTrack.currentposerror_size()&&i<3;i++)
    {
        target.currentPosError[i] = protoSeamTrack.currentposerror(i);
    }

    target.maxVel = protoSeamTrack.maxvel();

    target.maxAcc = protoSeamTrack.maxacc();

    target.paraChanged = (protoSeamTrack.parachanged()>0)? true:false;

    return true;
}

bool ProtoEncodeDecode::SeamTrackToProtoSeamTrack(const SeamTracking &source, aubo::robot::common::ProtoSeamTrack_t &protoSeamTrack)
{
    protoSeamTrack.set_trackenable( (source.trackEnable)? 1:0 );

    aubo::robot::common::ProtoRoadPoint *protoCurrentroadpoint = protoSeamTrack.mutable_currentroadpoint();

    roadPointTypeConversion(source.currentRoadPoint, *protoCurrentroadpoint);

    aubo::robot::common::ProtoRoadPoint *protoNextroadpoint = protoSeamTrack.mutable_nextroadpoint();
    roadPointTypeConversion(source.nextRoadPoint, *protoNextroadpoint);

    protoSeamTrack.set_timeinterval(source.timeInterval);

    for(int i=0;i<3;i++)
    {
        protoSeamTrack.add_currentposerror(source.currentPosError[i]);
    }

    protoSeamTrack.set_maxvel(source.maxVel);

    protoSeamTrack.set_maxacc(source.maxAcc);

    protoSeamTrack.set_parachanged((source.paraChanged)? 1:0 );


    return true;
}






void ProtoEncodeDecode::makeProtoCommunicationGeneralData(aubo::robot::communication::ProtoCommunicationGeneralData &protoCommunicationGeneralData, const std::vector<int> &propertyVector1, const std::vector<bool> &propertyVector2)
{
    protoCommunicationGeneralData.set_type(1);

    for(size_t i=0;i<propertyVector1.size();i++)
    {
        protoCommunicationGeneralData.add_property1(propertyVector1[i]);
    }

    for(size_t i=0;i<propertyVector2.size();i++)
    {
        protoCommunicationGeneralData.add_property2(propertyVector2[i]);
    }
}

bool ProtoEncodeDecode::ProtoCommunicationGeneralDataMessageSerialize(const aubo::robot::communication::ProtoCommunicationGeneralData &protoCommunicationGeneralData, char **ptr, int *size)
{
    bool ret = true;

    int  len = protoCommunicationGeneralData.ByteSize();

    char *buffer = new char[len];

    //序列化到数组
    if(protoCommunicationGeneralData.SerializeToArray(buffer, len) == true)
    {
        *size = len;

        *ptr  = buffer;
    }
    else
    {
        ret = false;

        *size = 0;

        *ptr  = NULL;

        delete buffer;

        std::cerr << "ERROR:ProtoCommunicationGeneralDataMessageSerialize Serialize fail."<<std::endl;
    }

    return ret;
}

void ProtoEncodeDecode::resolveProtoCommunicationGeneralData(const aubo::robot::communication::ProtoCommunicationGeneralData &protoCommunicationGeneralData,
                                                             std::vector<int> &propertyVector1, std::vector<bool> &propertyVector2, int &errorCode)
{
    propertyVector1.clear();
    propertyVector2.clear();

    for(int i=0; i<protoCommunicationGeneralData.property1_size(); i++)
    {
        propertyVector1.push_back(protoCommunicationGeneralData.property1(i));
    }

    for(int i=0; i<protoCommunicationGeneralData.property2_size(); i++)
    {
        propertyVector2.push_back(protoCommunicationGeneralData.property2(i));
    }

    if(protoCommunicationGeneralData.errorinfo_size()>0)
    {
        errorCode = protoCommunicationGeneralData.errorinfo(0).errorcode();
    }
}




bool ProtoEncodeDecode::ProtoCommunicationGeneralDataMessageParse(const char *buffer, int size, aubo::robot::communication::ProtoCommunicationGeneralData &protoCommunicationGeneralData)
{
    bool ret = true;


    //    std::cout<<"size:"<<size<<std::endl;

    //从数组反序列化
    if (protoCommunicationGeneralData.ParseFromArray(buffer,size) == false)
    {
        ret = false;

        std::cerr << "ERROR: ProtoCommunicationGeneralDataMessageParse fail." << std::endl;
    }

    return ret;
}

bool ProtoEncodeDecode::ProtoJointCommonDataToRobotStruct(const aubo::robot::communication::ProtoJointCommonData &protoJointCommonData, JointCommonData &jointCommonData)
{
    jointCommonData.JointCurVol     = protoJointCommonData.jointcurvol();
    jointCommonData.JointCurTemp    = protoJointCommonData.jointcurtemp();
    jointCommonData.JointWorkMode   = protoJointCommonData.jointworkmode();
    jointCommonData.JointDriEnable  = protoJointCommonData.jointdrienable();
    jointCommonData.JointOpenPwm    = protoJointCommonData.jointopenpwm();
    jointCommonData.JointTagCurrent = protoJointCommonData.jointtagcurrent();
    jointCommonData.JointTagSpeed   = protoJointCommonData.jointtagspeed();
    jointCommonData.JointTagPos     = protoJointCommonData.jointtagpos();

    jointCommonData.JointMaxCur     = protoJointCommonData.jointmaxcur();
    jointCommonData.JointMaxSpeed   = protoJointCommonData.jointmaxspeed();
    jointCommonData.JointMaxAcc     = protoJointCommonData.jointmaxacc();
    jointCommonData.JointMINPos     = protoJointCommonData.jointminpos();
    jointCommonData.JointMAXPos     = protoJointCommonData.jointmaxpos();

    jointCommonData.JointSEVLock    = protoJointCommonData.jointsevlock();
    jointCommonData.JointCurP       = protoJointCommonData.jointcurp();
    jointCommonData.JointCurI       = protoJointCommonData.jointcuri();
    jointCommonData.JointCurD       = protoJointCommonData.jointcurd();
    jointCommonData.JointSpeedP     = protoJointCommonData.jointspeedp();
    jointCommonData.JointSpeedI     = protoJointCommonData.jointspeedi();
    jointCommonData.JointSpeedD     = protoJointCommonData.jointspeedd();
    jointCommonData.JointSpeedDS    = protoJointCommonData.jointspeedds();
    jointCommonData.JointPosP       = protoJointCommonData.jointposp();
    jointCommonData.JointPosI       = protoJointCommonData.jointposi();
    jointCommonData.JointPosD       = protoJointCommonData.jointposd();
    jointCommonData.JointPosDS      = protoJointCommonData.jointposds();

    return true;
}



/** ************************************************************************************************************************************************
  *******************************************************生成数据流   数据序列化********************************************************************
  ***************************************************************************************************************************************************/


/** 生成数据: 登录 **/
bool ProtoEncodeDecode::getRequest_login(char **dataPtr, int *dataSize, const std::string &name, const std::string &passwd)
{
    int  size = 0;

    bool ret  = false;

    char *buffer = NULL;

    aubo::robot::communication::ProtoRequestLogin protoRequestLogin;

    protoRequestLogin.set_username(name.c_str(),  name.length()   );

    protoRequestLogin.set_passwd  (passwd.c_str(),passwd.length() );

    size    = protoRequestLogin.ByteSize();

    buffer  = new char[size];

    if(protoRequestLogin.SerializeToArray(buffer, size) == true)   //序列化到数组
    {
        *dataPtr  = buffer;

        *dataSize = size;

        ret = true;
    }
    else
    {
        delete buffer;

        *dataPtr   = NULL;

        *dataSize  = 0;

        std::cerr<<"ERROR: getRequest_login Serialize fail."<<std::endl;
    }

    return ret;
}


/** 生成数据: 路点类型 **/
bool ProtoEncodeDecode::getRequest_roadpoint(char **ptr, int *size, const wayPoint_S &roadPoint)
{
    bool ret = true;

    aubo::robot::common::ProtoRoadPoint protoRoadPoint;

    roadPointTypeConversion(roadPoint, protoRoadPoint);

    //序列化到数组
    int len = protoRoadPoint.ByteSize();

    char *buffer = new char[len];

    if(protoRoadPoint.SerializeToArray(buffer, len) == true)
    {
        *size = len;

        *ptr  = buffer;
    }
    else
    {
        ret = false;

        *size = 0;

        *ptr  = NULL;

        delete buffer;

        std::cout<<"getResponse_roadpoint Serialize fail."<<std::endl;
    }

    return ret;
}


/** 生成数据: 设置TCP参数 **/
bool ProtoEncodeDecode::getRequest_tcpParam(char **ptr, int *size, const RobotTcpParam &tcpParam)
{
    bool ret = true;

    aubo::robot::communication::RobotTcpParam protoRobotTcpParam;

    robotTcpParamTypeConversion(tcpParam, protoRobotTcpParam);

    //序列化到数组
    int len = protoRobotTcpParam.ByteSize();

    char *buffer = new char[len];

    if(protoRobotTcpParam.SerializeToArray(buffer, len) == true)
    {
        *size = len;

        *ptr  = buffer;
    }
    else
    {
        ret = false;

        *size = 0;

        *ptr  = NULL;

        delete buffer;

        std::cout<<"getRequest_tcpParam Serialize fail."<<std::endl;
    }

    return ret;
}

bool ProtoEncodeDecode::getRequest_setToolDynamicsParam(char **ptr, int *size, const ToolDynamicsParam &toolDynamicsParam)
{
    bool ret = false;

    aubo::robot::common::ToolParam protoToolParam;

    ret = toolParamTypeConversion(toolDynamicsParam, protoToolParam);

    if(ret == true)
    {
        //序列化到数组
        int len = protoToolParam.ByteSize();

        char *buffer = new char[len];

        if(protoToolParam.SerializeToArray(buffer, len) == true)
        {
            *size = len;

            *ptr  = buffer;

             ret = true;
        }
        else
        {
            *size = 0;

            *ptr  = NULL;

             delete buffer;

             W_ERROR("getRequest_setToolDynamicsParam Serialize fail.");
        }
    }

    return ret;
}


bool ProtoEncodeDecode::getRequest_setToolKinematicsParam(char **ptr, int *size, const ToolKinematicsParam &toolKinematicsParam)
{
    bool ret = false;

    aubo::robot::common::ToolKinematicsParam protoToolKinematicsParam;

    aubo::robot::common::Ori* pOri = protoToolKinematicsParam.mutable_toolori();
    pOri->set_w(toolKinematicsParam.toolInEndOrientation.w);
    pOri->set_x(toolKinematicsParam.toolInEndOrientation.x);
    pOri->set_y(toolKinematicsParam.toolInEndOrientation.y);
    pOri->set_z(toolKinematicsParam.toolInEndOrientation.z);

    aubo::robot::common::Pos* pPositopn = protoToolKinematicsParam.mutable_toolposition();
    pPositopn->set_x(toolKinematicsParam.toolInEndPosition.x);
    pPositopn->set_y(toolKinematicsParam.toolInEndPosition.y);
    pPositopn->set_z(toolKinematicsParam.toolInEndPosition.z);


    //序列化到数组
    int len = protoToolKinematicsParam.ByteSize();

    char *buffer = new char[len];

    if(protoToolKinematicsParam.SerializeToArray(buffer, len) == true)
    {
        *size = len;

        *ptr  = buffer;

         ret = true;
    }
    else
    {
        *size = 0;

        *ptr  = NULL;

         delete buffer;

         W_ERROR("getRequest_setToolDynamicsParam Serialize fail.");
    }

    return ret;
}

bool ProtoEncodeDecode::getRequest_startupOfflineExcitTraj(char **ptr, int *size, string name, int type, int subtype)
{
    bool ret = false;

    aubo::robot::communication::ProtoCommunicationOfflineExcitTraj protoCommunicationOfflineExcitTraj;

    protoCommunicationOfflineExcitTraj.set_trackfile(name.c_str(), name.length());
    protoCommunicationOfflineExcitTraj.set_type(type);
    protoCommunicationOfflineExcitTraj.set_subtype(subtype);


    //序列化到数组
    int len = protoCommunicationOfflineExcitTraj.ByteSize();

    char *buffer = new char[len];

    if(protoCommunicationOfflineExcitTraj.SerializeToArray(buffer, len) == true)
    {
        *size = len;

        *ptr  = buffer;

         ret = true;
    }
    else
    {
        *size = 0;

        *ptr  = NULL;

         delete buffer;

         W_ERROR("getRequest_startupOfflineExcitTraj Serialize fail.");
    }

    return ret;
}


/** 生成数据: 机械臂控制 **/
bool ProtoEncodeDecode::getRequest_robotControl(char **ptr, int *size, const RobotControlCommand cmd)
{
    bool ret = true;

    aubo::robot::communication::ProtoCommunicationGeneralData protoSetMaxAcc;

    std::vector<int>  propertyVector1;
    std::vector<bool> propertyVector2;

    propertyVector1.clear();
    propertyVector2.clear();

    propertyVector1.push_back(cmd);

    makeProtoCommunicationGeneralData(protoSetMaxAcc, propertyVector1, propertyVector2);

    if(ProtoCommunicationGeneralDataMessageSerialize(protoSetMaxAcc, ptr, size) == false)
    {
        ret = false;

        std::cerr << "ERROR:getRequest_robotControl Serialize fail."<<std::endl;
    }

    return ret;
}


/** 生成数据: 设置机械臂电源状态 **/
bool ProtoEncodeDecode::getRequest_setRobotPowerStatus(char **ptr, int *size, bool value)
{
    bool ret = true;

    aubo::robot::communication::ProtoCommunicationGeneralData protoSetMaxAcc;

    std::vector<int>  propertyVector1;
    std::vector<bool> propertyVector2;

    propertyVector1.clear();
    propertyVector2.clear();

    propertyVector2.push_back(value);

    makeProtoCommunicationGeneralData(protoSetMaxAcc, propertyVector1, propertyVector2);

    if(ProtoCommunicationGeneralDataMessageSerialize(protoSetMaxAcc, ptr, size) == false)
    {
        ret = false;

        std::cerr << "ERROR:setRobotBoardMaxAcc Serialize fail."<<std::endl;
    }

    return ret;
}


/** 生成数据: 设置机械臂工作模式 **/
bool ProtoEncodeDecode::getRequest_setRobotWorkMode(char **ptr, int *size, RobotWorkMode mode)
{
    bool ret = true;

    aubo::robot::communication::ProtoCommunicationGeneralData protoSetMaxAcc;

    std::vector<int>  propertyVector1;
    std::vector<bool> propertyVector2;

    propertyVector1.clear();
    propertyVector2.clear();

    propertyVector1.push_back(mode);

    makeProtoCommunicationGeneralData(protoSetMaxAcc, propertyVector1, propertyVector2);

    if(ProtoCommunicationGeneralDataMessageSerialize(protoSetMaxAcc, ptr, size) == false)
    {
        ret = false;

        std::cerr << "ERROR:setRobotWorkMode Serialize fail."<<std::endl;
    }

    return ret;
}


/** 生成数据: 设置机械臂运动控制 **/
bool ProtoEncodeDecode::getRequest_robotMoveControl(char **ptr, int *size, RobotMoveControlCommand value)
{
    bool ret = true;

    aubo::robot::communication::ProtoCommunicationGeneralData protoSetMaxAcc;

    std::vector<int>  propertyVector1;
    std::vector<bool> propertyVector2;

    propertyVector1.clear();
    propertyVector2.clear();

    propertyVector1.push_back(value);

    makeProtoCommunicationGeneralData(protoSetMaxAcc, propertyVector1, propertyVector2);

    if(ProtoCommunicationGeneralDataMessageSerialize(protoSetMaxAcc, ptr, size) == false)
    {
        ret = false;

        std::cerr << "ERROR:robotMoveControl Serialize fail."<<std::endl;
    }

    return ret;
}


/** 生成数据: 设置接口板最大加速度 **/
bool ProtoEncodeDecode::getRequest_setRobotBoardMaxAcc(char **ptr, int *size, int maxAcc)
{
    bool ret = true;

    aubo::robot::communication::ProtoCommunicationGeneralData protoSetMaxAcc;

    std::vector<int>  propertyVector1;
    std::vector<bool> propertyVector2;

    propertyVector1.clear();
    propertyVector2.clear();

    propertyVector1.push_back(maxAcc);

    makeProtoCommunicationGeneralData(protoSetMaxAcc, propertyVector1, propertyVector2);

    if(ProtoCommunicationGeneralDataMessageSerialize(protoSetMaxAcc, ptr, size) == false)
    {
        ret = false;

        std::cerr << "ERROR:setRobotBoardMaxAcc Serialize fail."<<std::endl;
    }

    return ret;
}


/** 生成数据: 设置碰撞等级 **/
bool ProtoEncodeDecode::getRequest_setRobotCollision(char **ptr, int *size, int grade)
{
    bool ret = true;

    aubo::robot::communication::ProtoCommunicationGeneralData protoSetRobotCollision;

    std::vector<int>  propertyVector1;
    std::vector<bool> propertyVector2;

    propertyVector1.clear();
    propertyVector2.clear();

    propertyVector1.push_back(grade);

    makeProtoCommunicationGeneralData(protoSetRobotCollision, propertyVector1, propertyVector2);

    if(ProtoCommunicationGeneralDataMessageSerialize(protoSetRobotCollision, ptr, size) == false)
    {
        ret = false;

        std::cerr << "ERROR: setRobotCollision Serialize fail."<<std::endl;
    }

    return ret;
}


bool ProtoEncodeDecode::getRequest_robotStartup(char **ptr, int *size, const RobotTcpParam &tcpParam, uint8 collisionClass, bool readPose,
                                                bool staticCollisionDetect,int maxAcc)
{
    bool ret = false;

    aubo::robot::communication::ProtoCommunicationRobotStartupProfile protoRobotStartupProfile;

    aubo::robot::communication::RobotTcpParam* protoTcpParam = protoRobotStartupProfile.mutable_tcpparam();

    robotTcpParamTypeConversion(tcpParam, *protoTcpParam);
    protoRobotStartupProfile.set_readpose(readPose);
    protoRobotStartupProfile.set_staticcollisiondetect(staticCollisionDetect);
    protoRobotStartupProfile.set_collisionclass(collisionClass);
    protoRobotStartupProfile.set_maxacc(maxAcc);

    //序列化到数组
    int len = protoRobotStartupProfile.ByteSize();

    char *buffer = new char[len];

    if(protoRobotStartupProfile.SerializeToArray(buffer, len) == true)
    {
        *size = len;

        *ptr  = buffer;

        ret = true;
    }
    else
    {
        *size = 0;

        *ptr  = NULL;

        delete buffer;

        std::cout<<"getRequest_robotStartup Serialize fail."<<std::endl;
    }

    return ret;
}

bool ProtoEncodeDecode::getResponse_Event(char *ptr, int *size, const RobotEventInfo &robotEvent)
{
    bool ret = false;

    aubo::robot::communication::ProtoCommunicationRobotEvent protoPushEvent;

    robotEventTypeConversion(robotEvent, protoPushEvent);

    //序列化到数组
    int len = protoPushEvent.ByteSize();

    if(len <= *size)
    {
        if(protoPushEvent.SerializeToArray(ptr, len) == true)
        {
            *size = len;

            ret = true;
        }
        else
        {
            *size = 0;

            W_ERROR("getResponse_Event Serialize fail.");
        }
    }

    return ret;
}

bool ProtoEncodeDecode::getRequest_setSafetyConfig(char **ptr, int *size, const RobotSafetyConfig &safetyConfig)
{
    bool ret = true;

    aubo::robot::common::ProtoResponseRobotSafetyConfig protoResponseRobotSafetyConfig;


    RobotSafetyConfigTypeConversion(safetyConfig, *(protoResponseRobotSafetyConfig.add_safetyconfig()));

    aubo::robot::common::RobotCommonResponse* errorInfo = protoResponseRobotSafetyConfig.mutable_errorinfo();

    errorInfo->set_errorcode(0);
    errorInfo->set_errormsg("");


    //序列化到数组
    int len = protoResponseRobotSafetyConfig.ByteSize();

    char *buffer = new char[len];

    if(protoResponseRobotSafetyConfig.SerializeToArray(buffer, len) == true)
    {
        *size = len;

        *ptr  = buffer;
    }
    else
    {
        ret = false;

        *size = 0;

        *ptr  = NULL;

        delete buffer;

        W_ERROR("getResponse_getGravityComponent Serialize fail.");
    }

    return ret;
}

bool ProtoEncodeDecode::getRequest_safeIoParamAbout(char **ptr, int *size, const std::vector<int> &paramVector)
{
    bool ret = true;

    aubo::robot::communication::protoSafeCommunicationParam protoSafeCommunicationParam;

    protoSafeCommunicationParam.set_paramreserved(0);

    for(int i=0;i<(int)paramVector.size();i++)
    {
        protoSafeCommunicationParam.add_param(paramVector[i]);
    }

    //序列化到数组
    int len = protoSafeCommunicationParam.ByteSize();

    char *buffer = new char[len];

    if(protoSafeCommunicationParam.SerializeToArray(buffer, len) == true)
    {
        *size = len;

        *ptr  = buffer;
    }
    else
    {
        ret = false;

        *size = 0;

        *ptr  = NULL;

        delete buffer;

        W_ERROR("getRequest_safeIoParamAbout Serialize fail.");
    }

    return ret;
}


/** 生成数据: 机械臂运动 **/
bool ProtoEncodeDecode::getRequest_robotMove(char **ptr, int *size, const RobotMoveProfile &moveProfile, const std::vector<wayPoint_S> &wayPointVector)
{
    bool ret = true;

    aubo::robot::common::RobotMove protoRobotMove;

    RobotMovePatamTypeConversion(moveProfile, wayPointVector, protoRobotMove);

    //序列化到数组
    int len = protoRobotMove.ByteSize();

    char *buffer = new char[len];

    if(protoRobotMove.SerializeToArray(buffer, len) == true)
    {
        *size = len;

        *ptr  = buffer;
    }
    else
    {
        ret = false;

        *size = 0;

        *ptr  = NULL;

        delete[] buffer;

        std::cout<<"getRequest_tcpParam Serialize fail."<<std::endl;
    }

    return ret;
}

bool ProtoEncodeDecode::getRequest_robotTeachMove(char **ptr, int *size, const RobotMoveProfile &moveProfile,
                                                  const CoordCalibrateByToolEndPoint &userCoordSystem)
{
    bool ret = true;

    aubo::robot::common::RobotTeachMove protoRobotTeachMove;

    AuboRobotMoveProfileType2ProtoType(moveProfile, *(protoRobotTeachMove.mutable_move_profile()));

    protoRobotTeachMove.set_coordinatesystemtype(userCoordSystem.coordType);

    for(size_t i=0;i<3;i++)
    {
        const wayPoint_S &roadpoint = userCoordSystem.wayPointArray[i];

        aubo::robot::common::ProtoRoadPoint *temp = protoRobotTeachMove.add_calibrateroadpointvector();

        roadPointTypeConversion(roadpoint, *temp);
    }
    protoRobotTeachMove.add_calibratemathod(userCoordSystem.methods);


    //序列化到数组
    int len = protoRobotTeachMove.ByteSize();

    char *buffer = new char[len];

    if(protoRobotTeachMove.SerializeToArray(buffer, len) == true)
    {
        *size = len;

        *ptr  = buffer;
    }
    else
    {
        ret = false;

        *size = 0;

        *ptr  = NULL;

        delete[] buffer;

        std::cout<<"getRequest_tcpParam Serialize fail."<<std::endl;
    }

    return ret;
}

bool ProtoEncodeDecode::getRequest_robotTeachMove(char **ptr, int *size, const RobotMoveProfile &moveProfile, const CoordCalibrateByJointAngleAndTool &userCoordSystem)
{
    bool ret = true;

    aubo::robot::common::RobotTeachMove protoRobotTeachMove;

    AuboRobotMoveProfileType2ProtoType(moveProfile, *(protoRobotTeachMove.mutable_move_profile()));

    //坐标系　类型＋３个点＋标定方法＋工具（工具在END坐标系下复用了）
    protoRobotTeachMove.set_coordinatesystemtype(userCoordSystem.coordType);

    protoRobotTeachMove.add_calibratemathod(userCoordSystem.methods);

    for(size_t i=0;i<3;i++)
    {
        wayPoint_S roadpoint;

        RobotUtilService::robotFk(userCoordSystem.wayPointArray[i].jointPos, ARM_DOF, roadpoint);

        aubo::robot::common::ProtoRoadPoint *temp = protoRobotTeachMove.add_calibrateroadpointvector();

        roadPointTypeConversion(roadpoint, *temp);
    }

    aubo::robot::common::ProtoToolInEndDesc* protoToolDesc=protoRobotTeachMove.add_toolinenddesc();

    aubo::robot::common::Ori* toolOri = protoToolDesc->mutable_toolinendorientation();
    toolOri->set_w(userCoordSystem.toolDesc.toolInEndOrientation.w);
    toolOri->set_x(userCoordSystem.toolDesc.toolInEndOrientation.x);
    toolOri->set_y(userCoordSystem.toolDesc.toolInEndOrientation.y);
    toolOri->set_z(userCoordSystem.toolDesc.toolInEndOrientation.z);

    aubo::robot::common::Pos* toolPos =  protoToolDesc->mutable_toolinendposition();

    toolPos->set_x(userCoordSystem.toolDesc.toolInEndPosition.x);
    toolPos->set_y(userCoordSystem.toolDesc.toolInEndPosition.y);
    toolPos->set_z(userCoordSystem.toolDesc.toolInEndPosition.z);

    //序列化到数组
    int len = protoRobotTeachMove.ByteSize();

    char *buffer = new char[len];

    if(protoRobotTeachMove.SerializeToArray(buffer, len) == true)
    {
        *size = len;

        *ptr  = buffer;
    }
    else
    {
        ret = false;

        *size = 0;

        *ptr  = NULL;

        delete buffer;

        std::cout<<"getRequest_tcpParam Serialize fail."<<std::endl;
    }

    return ret;
}

bool ProtoEncodeDecode::getRequest_firmwareUpgrade(char **ptr, int *size, int cmd, const void *firmwareContent, int length)
{
    bool ret = false;

    aubo::robot::communication::ProtoCommunicationRobotBoardFirmware protoBoardFirmware;

    protoBoardFirmware.set_command(cmd);
    protoBoardFirmware.set_firmwarecontentsize(length);
    protoBoardFirmware.set_firmwarecontent(firmwareContent, length);

    //序列化到数组
    int len = protoBoardFirmware.ByteSize();

    char *buffer = new char[len];

    if(protoBoardFirmware.SerializeToArray(buffer, len) == true)
    {
        *size = len;

        *ptr  = buffer;

         ret = true;
    }
    else
    {
        ret = false;

        *size = 0;

        *ptr  = NULL;

        delete buffer;

        std::cout<<"getRequest_firmwareUpgrade Serialize fail."<<std::endl;
    }

    return ret;
}



bool ProtoEncodeDecode::getRequest_robotDiagnosisIOData(char **ptr, int *size,  const std::vector<RobotDiagnosisIODesc> &diagnosisIOVector)
{
    bool ret = true;

    aubo::robot::communication::ProtoCommunicationRobotDiagnosisIOData protoCommunicationDiagnosisIOData;

    //初始化ProtoCommunicationRobotDiagnosisIOData
    protoCommunicationDiagnosisIOData.set_type(0);

    for(size_t i=0;i<diagnosisIOVector.size();i++)
    {
        aubo::robot::communication::ProtoRobotDiagnosisIODesc* protoIoDesc = protoCommunicationDiagnosisIOData.add_iodesc();

        RobotDiagnosisIODescTypeConversion(diagnosisIOVector[i], *protoIoDesc);
    }

    //序列化到数组
    int len = protoCommunicationDiagnosisIOData.ByteSize();

    char *buffer = new char[len];

    if(protoCommunicationDiagnosisIOData.SerializeToArray(buffer, len) == true)
    {
        *size = len;

        *ptr  = buffer;
    }
    else
    {
        ret = false;

        *size = 0;

        *ptr  = NULL;

        delete buffer;

        std::cerr << "ERROR:getResponse_robotDiagnosisIOData Serialize fail."<<std::endl;
    }

    return ret;
}



bool ProtoEncodeDecode::getRequest_robotAnalogIOData(char **ptr, int *size, const std::vector<RobotAnalogIODesc> &analogIOVector)
{
    bool ret = true;

    aubo::robot::communication::ProtoCommunicationRobotAnalogIOData protoCommunicationAnalogIOData;

    //初始化ProtoCommunicationRobotAnalogIOData
    protoCommunicationAnalogIOData.set_type(0);

    for(size_t i=0;i<analogIOVector.size();i++)
    {
        aubo::robot::communication::ProtoRobotAnalogIODesc* protoIoDesc = protoCommunicationAnalogIOData.add_iodesc();

        RobotAnalogIODescTypeConversion(analogIOVector[i], *protoIoDesc);
    }

    //序列化到数组
    int len = protoCommunicationAnalogIOData.ByteSize();

    char *buffer = new char[len];

    if(protoCommunicationAnalogIOData.SerializeToArray(buffer, len) == true)
    {
        *size = len;

        *ptr  = buffer;
    }
    else
    {
        ret = false;

        *size = 0;

        *ptr  = NULL;

        delete buffer;

        std::cerr << "ERROR:getResponse_robotAnalogIOData Serialize fail."<<std::endl;
    }

    return ret;
}

bool ProtoEncodeDecode::getRequest_robotRobotJointOffsetData(char **ptr, int *size, RobotJointOffset &jointOffset)
{
    bool ret = true;

    aubo::robot::common::RobotJointOffset protoResponseRobotJointOffset;


    protoResponseRobotJointOffset.set_joint1(jointOffset.jointOffset[0]);
    protoResponseRobotJointOffset.set_joint2(jointOffset.jointOffset[1]);
    protoResponseRobotJointOffset.set_joint3(jointOffset.jointOffset[2]);
    protoResponseRobotJointOffset.set_joint4(jointOffset.jointOffset[3]);
    protoResponseRobotJointOffset.set_joint5(jointOffset.jointOffset[4]);
    protoResponseRobotJointOffset.set_joint6(jointOffset.jointOffset[5]);

    //序列化到数组
    int len = protoResponseRobotJointOffset.ByteSize();

    char *buffer = new char[len];

    if(protoResponseRobotJointOffset.SerializeToArray(buffer, len) == true)
    {
        *size = len;

        *ptr  = buffer;
    }
    else
    {
        ret = false;

        *size = 0;

        *ptr  = NULL;

        delete buffer;

        W_ERROR("getRequest_robotRobotJointOffsetData Serialize fail.");
    }

    return ret;
}

bool ProtoEncodeDecode::getRequest_intTypeValueVector(char **ptr, int *size, const std::vector<int> &intTypeValueVector)
{
    bool ret = true;

    aubo::robot::communication::ProtoCommunicationIntVector protoCommunicationIntVector;

    protoCommunicationIntVector.set_num(intTypeValueVector.size());

    for(int i=0;i<(int)intTypeValueVector.size();i++)
    {
        protoCommunicationIntVector.add_value(intTypeValueVector[i]);
    }

    //序列化到数组
    int len = protoCommunicationIntVector.ByteSize();

    char *buffer = new char[len];

    if(protoCommunicationIntVector.SerializeToArray(buffer, len) == true)
    {
        *size = len;

        *ptr  = buffer;
    }
    else
    {
        ret = false;

        *size = 0;

        *ptr  = NULL;

        delete buffer;

        W_ERROR("getRequest_doubleValueVector Serialize fail.");
    }

    return ret;
}


bool ProtoEncodeDecode::getRequest_doubleTypeValueVector(char **ptr, int *size, const std::vector<double> &doubleTypeValueVector)
{
    bool ret = true;

    aubo::robot::communication::ProtoCommunicationDoubleVector protoCommunicationDoubleVector;

    protoCommunicationDoubleVector.set_num(doubleTypeValueVector.size());

    for(int i=0;i<(int)doubleTypeValueVector.size();i++)
    {
        protoCommunicationDoubleVector.add_value(doubleTypeValueVector[i]);
    }

    //序列化到数组
    int len = protoCommunicationDoubleVector.ByteSize();

    char *buffer = new char[len];

    if(protoCommunicationDoubleVector.SerializeToArray(buffer, len) == true)
    {
        *size = len;

        *ptr  = buffer;
    }
    else
    {
        ret = false;

        *size = 0;

        *ptr  = NULL;

        delete buffer;

        W_ERROR("getRequest_doubleValueVector Serialize fail.");
    }

    return ret;
}

bool ProtoEncodeDecode::getRequest_ConveyorTrackValuePoint(char **ptr, int *size, const Pos &pos, const Ori &ori, int timestamp)
{
    bool ret = false;

    aubo::robot::common::ProtoConveyorTrackValuePoint protoConveyorTrackValuePoint;

    aubo::robot::common::Ori* pOri = protoConveyorTrackValuePoint.mutable_ori();
    pOri->set_w(ori.w);
    pOri->set_x(ori.x);
    pOri->set_y(ori.y);
    pOri->set_z(ori.z);

    aubo::robot::common::Pos* pPositopn = protoConveyorTrackValuePoint.mutable_position();
    pPositopn->set_x(pos.x);
    pPositopn->set_y(pos.y);
    pPositopn->set_z(pos.z);

    protoConveyorTrackValuePoint.set_timestamp(timestamp);


    //序列化到数组
    int len = protoConveyorTrackValuePoint.ByteSize();

    char *buffer = new char[len];

    if(protoConveyorTrackValuePoint.SerializeToArray(buffer, len) == true)
    {
        *size = len;

        *ptr  = buffer;

         ret = true;
    }
    else
    {
        *size = 0;

        *ptr  = NULL;

         delete buffer;

         W_ERROR("getRequest_ConveyorTrackValuePoint Serialize fail.");
    }

    return ret;
}

bool ProtoEncodeDecode::getRequest_SeamTracking(char **ptr, int *size, const SeamTracking &seamTracking)
{
    bool ret = true;

    aubo::robot::common::ProtoSeamTrack_t protoSeamTrack;

    SeamTrackToProtoSeamTrack(seamTracking, protoSeamTrack);

    //序列化到数组
    int len = protoSeamTrack.ByteSize();

    char *buffer = new char[len];

    if(protoSeamTrack.SerializeToArray(buffer, len) == true)
    {
        *size = len;

        *ptr  = buffer;
    }
    else
    {
        ret = false;

        *size = 0;

        *ptr  = NULL;

        delete buffer;

        W_ERROR("getRequest_SeamTracking Serialize fail.");
    }

    return ret;
}







/** ************************************************************************************************************************************************
  *******************************************************解析数据流   数据反序列化********************************************************************
  ***************************************************************************************************************************************************/



/** 解析响应: 通用响应格式 **/
bool ProtoEncodeDecode::resolveResponse_commonFormat(const char *buffer, int size, CommunicationCommonResultResponse &response)
{
    bool ret = true;

    aubo::robot::communication::ProtoRobotCommonResponse protoCommonResponse;

    //从数组反序列化
    if (protoCommonResponse.ParseFromArray(buffer,size) == true)
    {
        response.m_errorCode = protoCommonResponse.errorcode();

        response.m_errorMsg  = protoCommonResponse.errormsg();
    }
    else
    {
        ret = false;

        std::cerr << "ERROR: resolveResponse_commonFormat Parse fail." << std::endl;
    }

    return ret;
}


/** 机械臂事件 **/
bool ProtoEncodeDecode::resolveResponse_robotEvent(const char *buffer, int size, RobotEventInfo &robotEvent)
{
    bool ret = false;

    aubo::robot::communication::ProtoCommunicationRobotEvent  protoRobotEvent;

    //从数组反序列化
    if (protoRobotEvent.ParseFromArray(buffer,size) == true)
    {
        robotEventTypeConversion(protoRobotEvent, robotEvent);

        ret = true;
    }
    else
    {
        std::cerr << "ERROR:resolveResponse_robotEvent Parse fail." << std::endl;
    }

    return ret;
}


/** 解析响应: 末端速度 **/
bool ProtoEncodeDecode::resolveResponse_robotEndSpeed(const char *buffer, int size, double &speed)
{
    bool ret = false;

    int  errorCode;

    std::vector<int>  propertyVector1;

    std::vector<bool> propertyVector2;

    aubo::robot::communication::ProtoCommunicationGeneralData protoCommunicationGeneralData;

    if( ProtoCommunicationGeneralDataMessageParse(buffer, size,  protoCommunicationGeneralData) == true)
    {
        resolveProtoCommunicationGeneralData(protoCommunicationGeneralData, propertyVector1,  propertyVector2, errorCode);

        if(propertyVector1.size()>0)
        {
            speed = propertyVector1[0]/1000.0;     //保留3位小数

            ret = true;
        }
    }

    return ret;
}

bool ProtoEncodeDecode::resolveResponse_movepProgressNotify(const char *buffer, int size, int &num)
{
    bool ret = false;

    int  errorCode;

    std::vector<int>  propertyVector1;

    std::vector<bool> propertyVector2;

    aubo::robot::communication::ProtoCommunicationGeneralData protoCommunicationGeneralData;

    if( ProtoCommunicationGeneralDataMessageParse(buffer, size,  protoCommunicationGeneralData) == true)
    {
        resolveProtoCommunicationGeneralData(protoCommunicationGeneralData, propertyVector1,  propertyVector2, errorCode);

        if(propertyVector1.size()>0)
        {
            num = propertyVector1[0];

            ret = true;
        }
    }

    return ret;
}

bool ProtoEncodeDecode::resolveResponse_jointAngle(const char *buffer, int size, JointParam &jointAngle, int &errorCode)
{
    bool ret = false;

    //从数组反序列化
    aubo::robot::common::ProtoJointAngleResponse protoJointAngleResponse;

    if (protoJointAngleResponse.ParseFromArray(buffer,size) == true)
    {
        jointAngle.jointPos[0] = protoJointAngleResponse.jointangle().joint1();
        jointAngle.jointPos[1] = protoJointAngleResponse.jointangle().joint2();
        jointAngle.jointPos[2] = protoJointAngleResponse.jointangle().joint3();
        jointAngle.jointPos[3] = protoJointAngleResponse.jointangle().joint4();
        jointAngle.jointPos[4] = protoJointAngleResponse.jointangle().joint5();
        jointAngle.jointPos[5] = protoJointAngleResponse.jointangle().joint6();

        if(protoJointAngleResponse.errorinfo_size()>0)
        {
            errorCode = protoJointAngleResponse.errorinfo(0).errorcode();

            ret = true;
        }
    }
    else
    {
        std::cerr << "resolveResponse_jointAngle Parse fail." << std::endl;
    }

    return ret;
}

bool ProtoEncodeDecode::resolveResponse_tcpforcesensorData(const char *buffer, int size, aubo_robot_namespace::ForceSensorData &data, int &errorCode)
{
    bool ret = false;

    //从数组反序列化
    aubo::robot::common::ProtoForceSensorDataResponse protoForceSensorDataResponse;

    if (protoForceSensorDataResponse.ParseFromArray(buffer,size) == true)
    {
        data.data[0] = protoForceSensorDataResponse.forcesensordata().data1();
        data.data[1] = protoForceSensorDataResponse.forcesensordata().data2();
        data.data[2] = protoForceSensorDataResponse.forcesensordata().data3();
        data.data[3] = protoForceSensorDataResponse.forcesensordata().data4();
        data.data[4] = protoForceSensorDataResponse.forcesensordata().data5();
        data.data[5] = protoForceSensorDataResponse.forcesensordata().data6();

        if(protoForceSensorDataResponse.errorinfo_size()>0)
        {
            errorCode = protoForceSensorDataResponse.errorinfo(0).errorcode();

            ret = true;
        }
    }
    else
    {
        std::cerr << "resolveResponse_tcpforcesensorData Parse fail." << std::endl;
    }

    return ret;
}

bool ProtoEncodeDecode::resolveResponse_waypoint(const char *buffer, int size, wayPoint_S &waypoint, int &errorCode)
{
    bool ret = false;

    //从数组反序列化
    aubo::robot::common::ProtoRoadPointResponse protoRoadPointResponse;

    if (protoRoadPointResponse.ParseFromArray(buffer,size) == true)
    {
        roadPointTypeConversion(protoRoadPointResponse.waypoint(),waypoint);

        if(protoRoadPointResponse.errorinfo_size()>0)
        {
            errorCode = protoRoadPointResponse.errorinfo(0).errorcode();

            ret = true;
        }
    }
    else
    {
        std::cerr << "resolveResponse_waypoint Parse fail." << std::endl;
    }

    return ret;
}


/** 解析请求: 机械臂关节状态 **/
bool ProtoEncodeDecode::resolveResponse_jointStatus(const char *buffer, int size,  JointStatus *jointStatusBuffer, int len, int &errorCode)
{
    bool ret = false;

    //从数组反序列化
    aubo::robot::common::ProtoRobotAllJointStatusResponse protoRobotAllJointStatusResponse;

    if (protoRobotAllJointStatusResponse.ParseFromArray(buffer,size) == true)
    {
        for(int i=0;i<protoRobotAllJointStatusResponse.jointstatus_size()&&i<len;i++)
        {
            jointStatusTypeConversion(protoRobotAllJointStatusResponse.jointstatus(i),jointStatusBuffer[i]);
        }

        if(protoRobotAllJointStatusResponse.errorinfo_size()>0)
        {
            errorCode = protoRobotAllJointStatusResponse.errorinfo(0).errorcode();
        }

        ret = true;
    }
    else
    {
        std::cerr << "resolveResponse_tcpParam Parse fail." << std::endl;
    }

    return ret;
}


/** 解析响应: 机械臂诊断信息 **/
bool ProtoEncodeDecode::resolveResponse_robotDiagnosis(const char *buffer, int size, RobotDiagnosis &robotDiagnosis)
{
    bool ret = false;

    //从数组反序列化
    aubo::robot::communication::RobotDiagnosis protoRobotDiagnosis;

    if (protoRobotDiagnosis.ParseFromArray(buffer,size) == true)
    {
        robotDiagnosisTypeConversionToStruct(protoRobotDiagnosis, robotDiagnosis);

        ret = true;
    }
    else
    {
        std::cerr << "resolveResponse_robotDiagnosis Parse fail." << std::endl;
    }

    return ret;
}


/** 解析响应: 机械臂诊断信息 **/
bool ProtoEncodeDecode::resolveResponse_robotDiagnosisInfo(const char *buffer, int size, RobotDiagnosis &robotDiagnosis, int &errorCode)
{
    bool ret = true;

    //从数组反序列化
    aubo::robot::communication::ProtoCommunicationRobotDiagnosisInfo protoRobotDiagnosis;

    if (protoRobotDiagnosis.ParseFromArray(buffer,size) == true)
    {
        errorCode = 0;

        if(protoRobotDiagnosis.errorinfo_size()>0)
        {
            errorCode = protoRobotDiagnosis.errorinfo(0).errorcode();
        }

        if(protoRobotDiagnosis.robotdiagnosis_size()>0)
        {
            robotDiagnosisTypeConversionToStruct(protoRobotDiagnosis.robotdiagnosis(0), robotDiagnosis);
        }
    }
    else
    {
        ret = false;

        std::cerr << "resolveResponse_robotDiagnosisInfo Parse fail." << std::endl;
    }

    return ret;
}


/** 解析响应:设备信息 **/
bool ProtoEncodeDecode::resolveResponse_devInfo(const char *buffer, int size, RobotDevInfo &devInfo, int &errorCode)
{
    bool ret = false;

    //从数组反序列化
    aubo::robot::common::ProtoResponseRobotDevInfo protoResponseRobotDevInfo;

    if (protoResponseRobotDevInfo.ParseFromArray(buffer,size) == true)
    {
        if(protoResponseRobotDevInfo.errorinfo().errorcode() == RobotResponseCode_SUCC)
        {
            RobotDevInfoTypeConversion(protoResponseRobotDevInfo.devinfo(0), devInfo);
        }

        errorCode = protoResponseRobotDevInfo.errorinfo().errorcode();

        ret = true;
    }
    else
    {
        std::cerr << "resolveResponse_devInfo Parse fail." << std::endl;
    }

    return ret;
}


/** 解析请求: 机械臂TCP参数**/
bool ProtoEncodeDecode::resolveResponse_tcpParam(const char *buffer, int size, RobotTcpParam &tcpParam, int &errorCode)
{
    bool ret = true;

    //从数组反序列化
    aubo::robot::common::ProtoResponseRobotTcpParam protoTcpParam;

    if (protoTcpParam.ParseFromArray(buffer,size) == true)
    {
        if(protoTcpParam.errorinfo().errorcode() == RobotResponseCode_SUCC)
        {
            robotTcpParamTypeConversion(protoTcpParam.tcpparam(0), tcpParam);
        }

        errorCode = protoTcpParam.errorinfo().errorcode();
    }
    else
    {
        ret = false;

        std::cerr << "resolveResponse_tcpParam Parse fail." << std::endl;
    }

    return ret;
}

bool ProtoEncodeDecode::resolveResponse_getToolDynamicsParam(const char *buffer, int size, ToolDynamicsParam &toolDynamicsParam, int &errorCode)
{
    bool ret = true;

    //从数组反序列化
    aubo::robot::common::ToolParam protoToolParam;

    if (protoToolParam.ParseFromArray(buffer,size) == true)
    {
        errorCode = protoToolParam.errorinfo().errorcode();

        if(errorCode == RobotResponseCode_SUCC)
        {
            ret = toolParamTypeConversion(protoToolParam, toolDynamicsParam);
        }
    }
    else
    {
        ret = false;

        std::cerr << "resolveResponse_getToolDynamicsParam Parse fail." << std::endl;
    }

    return ret;
}

bool ProtoEncodeDecode::resolveResponse_getToolKinematicsParam(const char *buffer, int size, ToolKinematicsParam &toolKinematicsParam, int &errorCode)
{
    bool ret = true;

    //从数组反序列化
    aubo::robot::common::ToolParam protoToolParam;

    if (protoToolParam.ParseFromArray(buffer,size) == true)
    {
        errorCode = protoToolParam.errorinfo().errorcode();

        if(errorCode == RobotResponseCode_SUCC)
        {
            ret = toolParamTypeConversion(protoToolParam, toolKinematicsParam);
        }
    }
    else
    {
        ret = false;

        std::cerr << "resolveResponse_getToolKinematicsParam Parse fail." << std::endl;
    }

    return ret;
}

bool ProtoEncodeDecode::resolveResponse_getDynIdentifyResults(const char *buffer, int size, std::vector<int> &paramVector, int &errorCode)
{
    bool ret = true;

    //从数组反序列化
    aubo::robot::communication::ProtoCommunicationDynIdentifyResults protoCommunicationDynIdentifyResults;

    if (protoCommunicationDynIdentifyResults.ParseFromArray(buffer,size) == true)
    {
        errorCode = protoCommunicationDynIdentifyResults.errorinfo(0).errorcode();

        if(errorCode == RobotResponseCode_SUCC)
        {
            paramVector.clear();

            for(int i=0;i<protoCommunicationDynIdentifyResults.param_size();i++)
            {
                paramVector.push_back(protoCommunicationDynIdentifyResults.param(i));
            }
        }
    }
    else
    {
        ret = false;

        std::cerr << "resolveResponse_getDynIdentifyResults Parse fail." << std::endl;
    }

    return ret;
}


/** 解析请求: 重力分量 **/
bool ProtoEncodeDecode::resolveResponse_gravityComponent(const char *buffer, int size, RobotGravityComponent &gravityComponent, int &errorCode)
{
    bool ret = true;

    //从数组反序列化
    aubo::robot::common::ProtoResponseRobotGravityComponent protoResponseRobotGravityComponent;

    if (protoResponseRobotGravityComponent.ParseFromArray(buffer,size) == true)
    {
        if(protoResponseRobotGravityComponent.errorinfo().errorcode() == RobotResponseCode_SUCC)
        {
            robotGravityComponentTypeConversion(protoResponseRobotGravityComponent.gravitycomponent(0), gravityComponent);
        }

        errorCode = protoResponseRobotGravityComponent.errorinfo().errorcode();
    }
    else
    {
        ret = false;

        std::cerr << "resolveResponse_gravityComponent Parse fail." << std::endl;
    }

    return ret;
}


/** 解析响应: 碰撞电流 **/
bool ProtoEncodeDecode::resolveResponse_collisionCurrent(const char *buffer, int size, RobotCollisionCurrent &collisionCurrent, int &errorCode)
{
    bool ret = true;

    //从数组反序列化
    aubo::robot::common::ProtoResponseRobotCollisionCurrent protoResponseRobotCollisionCurrent;

    if (protoResponseRobotCollisionCurrent.ParseFromArray(buffer,size) == true)
    {
        if(protoResponseRobotCollisionCurrent.errorinfo().errorcode() == RobotResponseCode_SUCC)
        {
            robotCollisionCurrentTypeConversion(protoResponseRobotCollisionCurrent.collisioncurrent(0), collisionCurrent);
        }

        errorCode = protoResponseRobotCollisionCurrent.errorinfo().errorcode();
    }
    else
    {
        ret = false;

        std::cerr << "resolveResponse_collisionCurrent Parse fail." << std::endl;
    }

    return ret;
}


/** 解析响应: 工作模式 **/
bool ProtoEncodeDecode::resolveResponse_robotWorkMode(const char *buffer, int size, RobotWorkMode &mode, int &errorCode)
{
    bool ret = false;

    std::vector<int>  propertyVector1;

    std::vector<bool> propertyVector2;

    aubo::robot::communication::ProtoCommunicationGeneralData protoCommunicationGeneralData;

    if( ProtoCommunicationGeneralDataMessageParse(buffer, size,  protoCommunicationGeneralData) == true)
    {
        resolveProtoCommunicationGeneralData(protoCommunicationGeneralData, propertyVector1,  propertyVector2, errorCode);

        if(propertyVector1.size()>0)
        {
            mode = (RobotWorkMode)propertyVector1[0];

            ret = true;
        }
    }

    return ret;
}

bool ProtoEncodeDecode::resolveResponse_robotState(const char *buffer, int size, RobotState &state, int &errorCode)
{
    bool ret = false;

    std::vector<int>  propertyVector1;

    std::vector<bool> propertyVector2;

    aubo::robot::communication::ProtoCommunicationGeneralData protoCommunicationGeneralData;

    if( ProtoCommunicationGeneralDataMessageParse(buffer, size,  protoCommunicationGeneralData) == true)
    {
        resolveProtoCommunicationGeneralData(protoCommunicationGeneralData, propertyVector1,  propertyVector2, errorCode);

        if(propertyVector1.size()>0)
        {
            state = (RobotState)propertyVector1[0];

            ret = true;
        }
    }

    return ret;
}


/** 解析响应: 真实机械臂是否存在 **/
bool ProtoEncodeDecode::resolveResponse_isRealRobotExist(const char *buffer, int size, bool &value, int &errorCode)
{
    bool ret = false;

    std::vector<int> propertyVector1;

    std::vector<bool> propertyVector2;

    aubo::robot::communication::ProtoCommunicationGeneralData protoCommunicationGeneralData;

    if( ProtoCommunicationGeneralDataMessageParse(buffer, size,  protoCommunicationGeneralData) == true)
    {
        resolveProtoCommunicationGeneralData(protoCommunicationGeneralData, propertyVector1,  propertyVector2, errorCode);

        if(propertyVector2.size()>0)
        {
            value = propertyVector2[0];

            ret = true;
        }
    }

    return ret;
}

/** 解析响应: 安全配置 **/
bool ProtoEncodeDecode::resolveResponse_safetyConfig(const char *buffer, int size, RobotSafetyConfig &safetyConfig, int &errorCode)
{
    bool ret = true;

    //从数组反序列化
    aubo::robot::common::ProtoResponseRobotSafetyConfig protoResponseRobotSafetyConfig;

    if (protoResponseRobotSafetyConfig.ParseFromArray(buffer,size) == true)
    {
        if(protoResponseRobotSafetyConfig.errorinfo().errorcode() == RobotResponseCode_SUCC)
        {
            RobotSafetyConfigTypeConversion(protoResponseRobotSafetyConfig.safetyconfig(0),safetyConfig);
        }

        errorCode = protoResponseRobotSafetyConfig.errorinfo().errorcode();
    }
    else
    {
        ret = false;

        std::cerr << "resolveResponse_safetyConfig Parse fail." << std::endl;
    }

    return ret;
}


/** 解析响应: 安全状态 **/
bool ProtoEncodeDecode::resolveResponse_safetyStatus(const char *buffer, int size, OrpeSafetyStatus &safetyStatus, int &errorCode)
{
    bool ret = true;

    //从数组反序列化
    aubo::robot::common::ProtoResponseOrpeSafetyStatus protoResponseOrpeSafetyStatus;

    if (protoResponseOrpeSafetyStatus.ParseFromArray(buffer,size) == true)
    {
        if(protoResponseOrpeSafetyStatus.errorinfo().errorcode() == RobotResponseCode_SUCC)
        {
            OrpeSafetyStatusTypeConversion(protoResponseOrpeSafetyStatus.safetystatus(0), safetyStatus);
        }

        errorCode = protoResponseOrpeSafetyStatus.errorinfo().errorcode();
    }
    else
    {
        ret = false;

        std::cerr << "resolveResponse_safetyStatus Parse fail." << std::endl;
    }

    return ret;
}

bool ProtoEncodeDecode::resolveResponse_DhParam(const char *buffer, int size, RobotType &robotType, RobotDhPara &robotDhPara, int &errorCode)
{
    bool ret = true;

    //从数组反序列化
    aubo::robot::common::ProtoResponseRobotDhParam protoResponseRobotDhParam;

    if (protoResponseRobotDhParam.ParseFromArray(buffer,size) == true)
    {
        errorCode = protoResponseRobotDhParam.errorinfo().errorcode();

        if(errorCode == RobotResponseCode_SUCC)
        {
            robotType = (RobotType)protoResponseRobotDhParam.robottype();

            // new DH model
            if ((protoResponseRobotDhParam.dhparam().a().size() == 6) &&
                    (protoResponseRobotDhParam.dhparam().alpha().size() == 6) &&
                    (protoResponseRobotDhParam.dhparam().d().size() == 6) &&
                    (protoResponseRobotDhParam.dhparam().theta().size() == 6)) {
                for (int i = 0; i < 6; i++) {
                    robotDhPara.a[i] = protoResponseRobotDhParam.dhparam().a(i);
                    robotDhPara.alpha[i] = protoResponseRobotDhParam.dhparam().alpha(i);
                    robotDhPara.d[i] = protoResponseRobotDhParam.dhparam().d(i);
                    robotDhPara.theta[i] = protoResponseRobotDhParam.dhparam().theta(i);
                }
            } else {
                for (int i = 0; i < 6; i++) {
                    robotDhPara.a[i] = 0;
                    robotDhPara.alpha[i] = 0;
                    robotDhPara.d[i] = 0;
                    robotDhPara.theta[i] = 0;
                }
                robotDhPara.a[2] = protoResponseRobotDhParam.dhparam().a3();
                robotDhPara.a[3] = protoResponseRobotDhParam.dhparam().a4();
                robotDhPara.d[0] = protoResponseRobotDhParam.dhparam().d1();
                robotDhPara.d[1] = protoResponseRobotDhParam.dhparam().d2();
                robotDhPara.d[4] = protoResponseRobotDhParam.dhparam().d5();
                robotDhPara.d[5] = protoResponseRobotDhParam.dhparam().d6();
                robotDhPara.alpha[0] = 0;
                robotDhPara.alpha[1] = -M_PI/2.;
                robotDhPara.alpha[2] = M_PI;
                robotDhPara.alpha[3] = M_PI;
                robotDhPara.alpha[4] = -M_PI/2.;
                robotDhPara.alpha[5] = M_PI/2.;

                robotDhPara.theta[0] = M_PI;
                robotDhPara.theta[1] = -M_PI/2.;
                robotDhPara.theta[2] = 0;
                robotDhPara.theta[3] = -M_PI/2.;
                robotDhPara.theta[4] = 0;
                robotDhPara.theta[5] = 0;
            }

            robotDhPara.A3 = protoResponseRobotDhParam.dhparam().a3();
            robotDhPara.A4 = protoResponseRobotDhParam.dhparam().a4();
            robotDhPara.D1 = protoResponseRobotDhParam.dhparam().d1();
            robotDhPara.D2 = protoResponseRobotDhParam.dhparam().d2();
            robotDhPara.D5 = protoResponseRobotDhParam.dhparam().d5();
            robotDhPara.D6 = protoResponseRobotDhParam.dhparam().d6();
        }
    }
    else
    {
        ret = false;

        std::cerr << "resolveResponse_DhParam Parse fail." << std::endl;
    }

    return ret;
}

bool ProtoEncodeDecode::resolveResponse_moveControlResule(const char *buffer, int size, call_robot_motion_func_result &funcRet, int &errorCode)
{
    bool ret = true;

    //从数组反序列化
    aubo::robot::common::ProtoRobotMoveFuncResult protoRobotMoveFuncResult;

    if (protoRobotMoveFuncResult.ParseFromArray(buffer,size) == true)
    {
        errorCode = protoRobotMoveFuncResult.errorinfo().errorcode();

        if(errorCode == RobotResponseCode_SUCC)
        {
            funcRet = (call_robot_motion_func_result)protoRobotMoveFuncResult.ret();
        }
    }
    else
    {
        ret = false;

        std::cerr << "resolveResponse_moveControlResule Parse fail." << std::endl;
    }

    return ret;
}



bool ProtoEncodeDecode::resolveResponse_robotDiagnosisIOData(const char *buffer, int size, std::vector<RobotDiagnosisIODesc> &diagnosisIOStatusVector, int &errorCode)
{
    bool ret = true;

    aubo::robot::communication::ProtoCommunicationRobotDiagnosisIOData  protoCommunicationDiagnosisIOData;

    diagnosisIOStatusVector.clear();

    //从数组反序列化
    if (protoCommunicationDiagnosisIOData.ParseFromArray(buffer,size) == true)
    {
        for(int i=0; i<protoCommunicationDiagnosisIOData.iodesc_size(); i++)
        {
            RobotDiagnosisIODesc iodesc;

            RobotDiagnosisIODescTypeConversion(protoCommunicationDiagnosisIOData.iodesc(i), iodesc);

            diagnosisIOStatusVector.push_back(iodesc);
        }

        errorCode  = protoCommunicationDiagnosisIOData.errorinfo(0).errorcode();
    }
    else
    {
        ret = false;

        std::cerr << "ERROR:resolveRequest_robotDiagnosisIOData Parse fail." << std::endl;
    }
    return ret;
}


bool ProtoEncodeDecode::resolveResponse_robotAnalogIOData(const char *buffer, int size, std::vector<RobotAnalogIODesc> &analogIOStatusVector, int &errorCode)
{
    bool ret = true;

    aubo::robot::communication::ProtoCommunicationRobotAnalogIOData protoCommunicationAnalogIOData;

    analogIOStatusVector.clear();

    //从数组反序列化
    if (protoCommunicationAnalogIOData.ParseFromArray(buffer,size) == true)
    {
        for(int i=0; i<protoCommunicationAnalogIOData.iodesc_size(); i++)
        {
            RobotAnalogIODesc iodesc;

            RobotAnalogIODescTypeConversion(protoCommunicationAnalogIOData.iodesc(i), iodesc);

            analogIOStatusVector.push_back(iodesc);
        }

        errorCode  = protoCommunicationAnalogIOData.errorinfo(0).errorcode();
    }
    else
    {
        ret = false;

        std::cerr << "ERROR:resolveRequest_robotAnalogIOData Parse fail." << std::endl;
    }
    return ret;
}


bool ProtoEncodeDecode::resolveResponse_robotToolIoStatus(const char *buffer, int size, RobotToolAllIOStatus &toolAllIOStatus, int &errorCode)
{
    bool ret = true;

    aubo::robot::communication::ProtoCommunicationToolAllIOStatusInfoResponse protoCommunicationToolAllIOStatus;

    //从数组反序列化
    if (protoCommunicationToolAllIOStatus.ParseFromArray(buffer,size) == true)
    {
        toolAllIOStatus.powerType         = (ToolPowerType)protoCommunicationToolAllIOStatus.tooliostatus().powertype();
        toolAllIOStatus.systemVoltage     = protoCommunicationToolAllIOStatus.tooliostatus().systemvoltage();
        toolAllIOStatus.systemTemperature = protoCommunicationToolAllIOStatus.tooliostatus().systemtemperature();
        toolAllIOStatus.errorStatus       = protoCommunicationToolAllIOStatus.errorinfo().errorcode();

        for(int i=0;i<protoCommunicationToolAllIOStatus.tooliostatus().digitaliostatus_size()&&i<4;i++)
        {
            toolAllIOStatus.digitalIoStatus[i].ioData = protoCommunicationToolAllIOStatus.tooliostatus().digitaliostatus(i).iodata();
            toolAllIOStatus.digitalIoStatus[i].ioType = (ToolIOType)protoCommunicationToolAllIOStatus.tooliostatus().digitaliostatus(i).iotype();
        }

        for(int i=0;i<protoCommunicationToolAllIOStatus.tooliostatus().aidata_size()&&i<2;i++)
        {
            toolAllIOStatus.aiData[i] = protoCommunicationToolAllIOStatus.tooliostatus().aidata(i);
        }

        errorCode  = protoCommunicationToolAllIOStatus.errorinfo().errorcode();
    }
    else
    {
        ret = false;

        std::cerr << "ERROR:resolveRequest_robotAnalogIOData Parse fail." << std::endl;
    }

    return ret;
}


bool ProtoEncodeDecode::resolveResponse_ethernetDeviceName(const char *buffer, int size, string &ethernetDeviceName, int &errorCode)
{
    bool ret = true;

    aubo::robot::communication::ProtoCommunicationEthernetDeviceNameResponse protoCommunicationEthernetDeviceNameResponse;

    //从数组反序列化
    if (protoCommunicationEthernetDeviceNameResponse.ParseFromArray(buffer,size) == true)
    {
        ethernetDeviceName = protoCommunicationEthernetDeviceNameResponse.name();

        errorCode  = protoCommunicationEthernetDeviceNameResponse.errorinfo().errorcode();
    }
    else
    {
        ret = false;

        std::cerr << "ERROR:resolveResponse_ethernetDeviceName Parse fail." << std::endl;
    }

    return ret;
}

bool ProtoEncodeDecode::resolveResponse_jointCommonData(const char *buffer, int size, std::vector<JointCommonData> &jointCommonDataVector, int &errorCode)
{
    bool ret = true;

    aubo::robot::communication::ProtoJointCommonDataResponse protoJointCommonDataResponse;

    //从数组反序列化
    if (protoJointCommonDataResponse.ParseFromArray(buffer,size) == true)
    {
        for(int i=0;i<protoJointCommonDataResponse.jointcommondata_size();i++)
        {
            JointCommonData jointCommonData;

            ProtoJointCommonDataToRobotStruct(protoJointCommonDataResponse.jointcommondata(i), jointCommonData);

            jointCommonDataVector.push_back(jointCommonData);
        }

        errorCode  = protoJointCommonDataResponse.errorinfo().errorcode();
    }
    else
    {
        ret = false;

        std::cerr << "ERROR:resolveRequest_jointCommonData Parse fail." << std::endl;
    }

    return ret;
}

bool ProtoEncodeDecode::resolveResponse_intTypeValueVector(const char *buffer, int size, std::vector<int> &intTypeValueVector, int &errorCode)
{
    bool ret = true;

    //从数组反序列化
    aubo::robot::communication::ProtoCommunicationIntVectorResponse protoCommunicationIntVectorResponse;

    if (protoCommunicationIntVectorResponse.ParseFromArray(buffer,size) == true)
    {
        intTypeValueVector.clear();

        for(int i=0;i<protoCommunicationIntVectorResponse.value_size();i++)
        {
            intTypeValueVector.push_back(protoCommunicationIntVectorResponse.value(i));
        }

        errorCode = protoCommunicationIntVectorResponse.errorinfo().errorcode();
    }
    else
    {
        ret = false;

        std::cerr << "resolveRequest_intTypeValueVector Parse fail." << std::endl;
    }

    return ret;
}

bool ProtoEncodeDecode::resolveResponse_doubleTypeValueVector(const char *buffer, int size, std::vector<double> &doubleTypeValueVector, int &errorCode)
{
    bool ret = true;

    //从数组反序列化
    aubo::robot::communication::ProtoCommunicationDoubleVectorResponse protoCommunicationDoubleVectorResponse;

    if (protoCommunicationDoubleVectorResponse.ParseFromArray(buffer,size) == true)
    {
        doubleTypeValueVector.clear();

        for(int i=0;i<protoCommunicationDoubleVectorResponse.value_size();i++)
        {
            doubleTypeValueVector.push_back(protoCommunicationDoubleVectorResponse.value(i));
        }

        errorCode = protoCommunicationDoubleVectorResponse.errorinfo().errorcode();
    }
    else
    {
        ret = false;

        std::cerr << "resolveRequest_doubleTypeValueVector Parse fail." << std::endl;
    }

    return ret;
}

bool ProtoEncodeDecode::resolveResponse_SeamTracking(const char *buffer, int size, SeamTracking &seamTracking, int &errorCode)
{
    bool ret = true;

    aubo::robot::common::ProtoSeamTrackResponse_t protoSeamTrackResponse;

    //从数组反序列化
    if (protoSeamTrackResponse.ParseFromArray(buffer,size) == true)
    {
        ProtoSeamTrackToSeamTrack(protoSeamTrackResponse.seamtrack(), seamTracking);

        errorCode  = protoSeamTrackResponse.errorinfo().errorcode();
    }
    else
    {
        ret = false;

        std::cerr << "ERROR:resolveResponse_SeamTracking Parse fail." << std::endl;
    }

    return ret;
}

bool ProtoEncodeDecode::ProtoWrenchToWrench(const peripheral::ProtoWrench &source, WrenchParam &target)
{
    for(int i=0;i<3;i++)
    {
        target.force[i]  = source.force(i);
        target.torque[i] = source.torque(i);
    }
    return true;
}

bool ProtoEncodeDecode::WrenchToProtoWrench(const WrenchParam &source, peripheral::ProtoWrench &target)
{
    for(int i=0;i<3;i++)
    {
        target.add_force(source.force[i]);
        target.add_torque(source.torque[i]);
    }
    return true;
}

bool ProtoEncodeDecode::ProtoFtSensorCalResultToRobotType(const peripheral::ProtoFtSensorCalibResult &source, FtSensorCalResult &target)
{
    ProtoWrenchToWrench(source.wrench(), target.offset);

    target.mass = source.mass();                  //质量

    for(int i=0;i<3;i++)
        target.centroid[i] = source.centroid(i);  //质心

    target.angle[0] = source.angle(0);
    target.angle[1] = source.angle(1);            //安装角度

    return true;
}

bool ProtoEncodeDecode::FtSensorCalResultToProtoType(const FtSensorCalResult &source, peripheral::ProtoFtSensorCalibResult &target)
{
    WrenchToProtoWrench(source.offset, *(target.mutable_wrench()));

    target.set_mass(source.mass);                 //质量

    for(int i=0;i<3;i++)
        target.add_centroid(source.centroid[i]);  //质心

    target.add_angle(source.angle[0]);
    target.add_angle(source.angle[1]);          //安装角度

    return true;
}

bool ProtoEncodeDecode::FtSensorCalibParamToProtoType(const JointParam jointParamGroup[3], const WrenchParam wrenchParamGroup[3], peripheral::ProtoFtSensorCalibParam &target)
{
    aubo::robot::common::ProtoJointAngle* protoJointAnglePtr;

    for(int i=0;i<3;i++)
    {
        protoJointAnglePtr = target.add_jointangle();

        protoJointAnglePtr->set_joint1(jointParamGroup[i].jointPos[0]);
        protoJointAnglePtr->set_joint2(jointParamGroup[i].jointPos[1]);
        protoJointAnglePtr->set_joint3(jointParamGroup[i].jointPos[2]);
        protoJointAnglePtr->set_joint4(jointParamGroup[i].jointPos[3]);
        protoJointAnglePtr->set_joint5(jointParamGroup[i].jointPos[4]);
        protoJointAnglePtr->set_joint6(jointParamGroup[i].jointPos[5]);
    }

    for(int i=0;i<3;i++)
    {
        WrenchToProtoWrench(wrenchParamGroup[i], *target.add_wrenchlist());
    }

    return true;
}


bool ProtoEncodeDecode::getRequest_FtSensorCalibParam(char **ptr, int *size, JointParam jointParamGroup[], WrenchParam wrenchGroup[])
{
    peripheral::ProtoFtSensorCalibParam protoFtSensorCalibParam;
    for(int i=0;i<3;i++)
    {
        aubo::robot::common::ProtoJointAngle* protoJointAnglePtr = protoFtSensorCalibParam.add_jointangle();

        protoJointAnglePtr->set_joint1(jointParamGroup[i].jointPos[0]);
        protoJointAnglePtr->set_joint2(jointParamGroup[i].jointPos[1]);
        protoJointAnglePtr->set_joint3(jointParamGroup[i].jointPos[2]);
        protoJointAnglePtr->set_joint4(jointParamGroup[i].jointPos[3]);
        protoJointAnglePtr->set_joint5(jointParamGroup[i].jointPos[4]);
        protoJointAnglePtr->set_joint6(jointParamGroup[i].jointPos[5]);
    }

    for(int i=0;i<3;i++)
    {
        WrenchToProtoWrench(wrenchGroup[i], *protoFtSensorCalibParam.add_wrenchlist());
    }

    //序列化到数组
    return ProtobufDataSerializeToArray(&protoFtSensorCalibParam, ptr, size);
}

bool ProtoEncodeDecode::resolveResponse_FtSensorCalibResult(const char *buffer, int size, FtSensorCalResult &calibrationResult, int &errorCode)
{
    bool ret = false;

    peripheral::ProtoResponseFtSensorCalibResult protoResponseFtSensorCalibResult;

    //从数组反序列化
    if (protoResponseFtSensorCalibResult.ParseFromArray(buffer,size) == true)
    {
        errorCode  = protoResponseFtSensorCalibResult.errorinfo().errorcode();

        if(errorCode == 0)
            ProtoFtSensorCalResultToRobotType(protoResponseFtSensorCalibResult.ftsensorcalibresult(0), calibrationResult);
    }
    else
    {
        ret = false;

        std::cerr << "ERROR:resolveResponse_FtSensorCalibResult Parse fail." << std::endl;
    }

    return ret;
}

bool ProtoEncodeDecode::resolveResponse_Wrench(const char *buffer, int size, WrenchParam &wrenchParam, int &errorCode)
{
    bool ret = false;

    peripheral::ProtoResponseWrench protoResponseWrench;

    //从数组反序列化
    if (protoResponseWrench.ParseFromArray(buffer,size) == true)
    {
        errorCode  = protoResponseWrench.errorinfo().errorcode();

        if(errorCode == 0)
            ProtoWrenchToWrench(protoResponseWrench.wrench(0), wrenchParam);
    }
    else
    {
        ret = false;

        std::cerr << "ERROR:resolveResponse_FtSensorCalibResult Parse fail." << std::endl;
    }

    return ret;
}

bool ProtoEncodeDecode::getResponse_Wrench(char **ptr, int *size, WrenchParam &wrenchData, int errorCode, const string &errorMsg)
{
    peripheral::ProtoResponseWrench protoResponseWrench;

    //repeated wrench
    WrenchToProtoWrench(wrenchData, *protoResponseWrench.add_wrench());

    //required errorInfo
    ProtoRobotCommonResponseInit(protoResponseWrench.mutable_errorinfo(), errorCode, errorMsg);

    //序列化到数组
    return ProtobufDataSerializeToArray(&protoResponseWrench, ptr, size);
}

bool ProtoEncodeDecode::getResponse_FtSensorCalibResult(char **ptr, int *size, const FtSensorCalResult &calibrationResult, int errorCode, const string &errorMsg)
{
    peripheral::ProtoResponseFtSensorCalibResult protoResponseFtSensorCalibResult;

    //repeated  ftSensorCalibResult
    FtSensorCalResultToProtoType(calibrationResult, *protoResponseFtSensorCalibResult.add_ftsensorcalibresult());

    //required errorInfo
    ProtoRobotCommonResponseInit(protoResponseFtSensorCalibResult.mutable_errorinfo(), errorCode, errorMsg);

    //序列化到数组
    return ProtobufDataSerializeToArray(&protoResponseFtSensorCalibResult, ptr, size);
}

void ProtoEncodeDecode::ProtoRobotCommonResponseInit(aubo::robot::common::RobotCommonResponse *protoErrnoInfo, int errorCode, const string &errorMsg)
{
    protoErrnoInfo->set_errorcode(errorCode);
    protoErrnoInfo->set_errormsg(errorMsg.c_str());
}

bool ProtoEncodeDecode::ProtobufDataSerializeToArray(const google::protobuf::Message *msg, char **ptr, int *size)
{
    int  len     = msg->ByteSize();
    char *buffer = new char[len];
    int  serializeResult = msg->SerializeToArray(buffer, len);   //序列化到数组

    if(serializeResult)
    {
        *size = len;
        *ptr  = buffer;
    }
    else
    {
        *size = 0;
        *ptr  = NULL;
        delete[] buffer;
        W_ERROR(" Serialize fail.");
    }

    return serializeResult;
}

bool ProtoEncodeDecode::RobotInfo_ProtoToAubo(const aubo::robot::paramerter::ProtoRobotInfo &protoRobotInfo, RobotInfo &robotInfo)
{
    if(protoRobotInfo.reserve_size() != 12 )
        return false;
    if(protoRobotInfo.joint_duration_size() != 6 )
        return false;

    robotInfo.robot_type     = (uint16)protoRobotInfo.robot_type();
    robotInfo.auth_type      = (uint16)protoRobotInfo.auth_type();
    robotInfo.robot_expire   = (uint32)protoRobotInfo.robot_expire();
    robotInfo.robot_duration = (uint32)protoRobotInfo.robot_duration();

    for(int i=0;i<12;i++)
    {
        robotInfo.reserve[i] = (uint8)protoRobotInfo.reserve(i);
    }

    for(int i=0;i<6;i++)
    {
        robotInfo.joint_duration[i] = (uint32)protoRobotInfo.joint_duration(i);
    }

    return true;
}

bool ProtoEncodeDecode::RobotInfo_AuboToProto(const RobotInfo &robotInfo, aubo::robot::paramerter::ProtoRobotInfo &protoRobotInfo)
{
    protoRobotInfo.set_robot_type(robotInfo.robot_type);
    protoRobotInfo.set_auth_type(robotInfo.auth_type);
    protoRobotInfo.set_robot_expire(robotInfo.robot_expire);
    protoRobotInfo.set_robot_duration(robotInfo.robot_duration);

    for(int i=0;i<12;i++)
    {
        protoRobotInfo.add_reserve(robotInfo.reserve[i]);
    }
    for(int i=0;i<6;i++)
    {
        protoRobotInfo.add_joint_duration(robotInfo.joint_duration[i]);
    }
    return true;
}

bool ProtoEncodeDecode::RobotDynamicsParameters_ProtoToAubo(const aubo::robot::paramerter::ProtoRobotDynamicsParameter &ProtoDynamicsParameter, RobotDynamicsParameters &dynamicsParameter)
{
    if(ProtoDynamicsParameter.k_size() !=6 ) return false;
    if(ProtoDynamicsParameter.ia_size()!=4 ) return false;
    if(ProtoDynamicsParameter.m_size() !=1 ) return false;
    if(ProtoDynamicsParameter.mxyz_size()!=13 ) return false;
    if(ProtoDynamicsParameter.ixyz_size()!=28 ) return false;
    if(ProtoDynamicsParameter.cb_size()!=6 ) return false;

    for(int i=0;i<6;i++)
        dynamicsParameter.K[i] = ProtoDynamicsParameter.k(i);

    for(int i=0;i<4;i++)
        dynamicsParameter.IA[i] = ProtoDynamicsParameter.ia(i);

    for(int i=0;i<1;i++)
        dynamicsParameter.M[i] = ProtoDynamicsParameter.m(i);

    for(int i=0;i<13;i++)
        dynamicsParameter.MXYZ[i] = ProtoDynamicsParameter.mxyz(i);

    for(int i=0;i<28;i++)
        dynamicsParameter.IXYZ[i] = ProtoDynamicsParameter.ixyz(i);

    for(int i=0;i<6;i++)
        dynamicsParameter.CB[i] = ProtoDynamicsParameter.cb(i);

    return true;
}

bool ProtoEncodeDecode::RobotDynamicsParameters_AuboToProto(const RobotDynamicsParameters &dynamicsParameter, aubo::robot::paramerter::ProtoRobotDynamicsParameter &ProtoDynamicsParameter)
{
    ProtoDynamicsParameter.clear_k();
    for(int i=0;i<6;i++)
        ProtoDynamicsParameter.add_k(dynamicsParameter.K[i]);

    ProtoDynamicsParameter.clear_ia();
    for(int i=0;i<4;i++)
        ProtoDynamicsParameter.add_ia(dynamicsParameter.IA[i]);

    ProtoDynamicsParameter.clear_m();
    for(int i=0;i<1;i++)
        ProtoDynamicsParameter.add_m(dynamicsParameter.M[i]);

    ProtoDynamicsParameter.clear_mxyz();
    for(int i=0;i<13;i++)
        ProtoDynamicsParameter.add_mxyz(dynamicsParameter.MXYZ[i]);

    ProtoDynamicsParameter.clear_ixyz();
    for(int i=0;i<28;i++)
        ProtoDynamicsParameter.add_ixyz(dynamicsParameter.IXYZ[i]);

    ProtoDynamicsParameter.clear_cb();
    for(int i=0;i<6;i++)
        ProtoDynamicsParameter.add_cb(dynamicsParameter.CB[i]);

    return true;
}

bool ProtoEncodeDecode::RobotHandguidingParameter_ProtoToAubo(const aubo::robot::paramerter::ProtoRobotHandguidingParameter &ProtoHandguidingParameter, RobotHandguidingParameters &handguidingParameters)
{
    if(ProtoHandguidingParameter.fp_size()!=6) return false;
    if(ProtoHandguidingParameter.fd_size()!=6) return false;
    if(ProtoHandguidingParameter.fk_size()!=6) return false;
    if(ProtoHandguidingParameter.fm_size()!=6) return false;
    if(ProtoHandguidingParameter.pos_limit_size()!=6) return false;
    if(ProtoHandguidingParameter.velocity_limit_size()!=6) return false;
    if(ProtoHandguidingParameter.acceleration_limit_size()!=6) return false;

    for(int i=0;i<6;i++)
    {
        handguidingParameters.FP[i] = ProtoHandguidingParameter.fp(i);
        handguidingParameters.FD[i] = ProtoHandguidingParameter.fd(i);
        handguidingParameters.FK[i] = ProtoHandguidingParameter.fk(i);
        handguidingParameters.FM[i] = ProtoHandguidingParameter.fm(i);
        handguidingParameters.pos_limit[i] = ProtoHandguidingParameter.pos_limit(i);
        handguidingParameters.velocity_limit[i] = ProtoHandguidingParameter.velocity_limit(i);
        handguidingParameters.acceleration_limit[i] = ProtoHandguidingParameter.acceleration_limit(i);
    }
    return true;
}

bool ProtoEncodeDecode::RobotHandguidingParameter_AuboToProto(const RobotHandguidingParameters &handguidingParameters, aubo::robot::paramerter::ProtoRobotHandguidingParameter &ProtoHandguidingParameter)
{
    for(int i=0;i<6;i++)
    {
        ProtoHandguidingParameter.add_fp(handguidingParameters.FP[i]);
        ProtoHandguidingParameter.add_fd(handguidingParameters.FD[i]);
        ProtoHandguidingParameter.add_fk(handguidingParameters.FK[i]);
        ProtoHandguidingParameter.add_fm(handguidingParameters.FM[i]);
        ProtoHandguidingParameter.add_pos_limit(handguidingParameters.pos_limit[i]);
        ProtoHandguidingParameter.add_velocity_limit(handguidingParameters.velocity_limit[i]);
        ProtoHandguidingParameter.add_acceleration_limit(handguidingParameters.acceleration_limit[i]);
    }
    return true;
}

bool ProtoEncodeDecode::RobotKinematicsParameter_ProtoToAubo(const aubo::robot::paramerter::ProtoRobotKinematicsParameter &protoRobotKinematicsParameter, RobotKinematicsParameters &kinematicsParameter)
{
    if(protoRobotKinematicsParameter.da_size()!=6) return false;
    if(protoRobotKinematicsParameter.dd_size()!=6) return false;
    if(protoRobotKinematicsParameter.dalpha_size()!=6) return false;
    if(protoRobotKinematicsParameter.dbeta_size()!=6) return false;
    if(protoRobotKinematicsParameter.dratio_size()!=6) return false;
    if(protoRobotKinematicsParameter.dtheta_size()!=6) return false;

    for(int i=0;i<6;i++)
    {
        kinematicsParameter.da[i]     = protoRobotKinematicsParameter.da(i);
        kinematicsParameter.dd[i]     = protoRobotKinematicsParameter.dd(i);
        kinematicsParameter.dalpha[i] = protoRobotKinematicsParameter.dalpha(i);
        kinematicsParameter.dbeta[i]  = protoRobotKinematicsParameter.dbeta(i);
        kinematicsParameter.dratio[i] = protoRobotKinematicsParameter.dratio(i);
        kinematicsParameter.dtheta[i] = protoRobotKinematicsParameter.dtheta(i);
    }
    return true;

}

bool ProtoEncodeDecode::RobotKinematicsParameter_ProtoToAubo_Legacy(const aubo::robot::paramerter::ProtoRobotKinematicsParameter_legacy &protoRobotKinematicsParameter, aubo_robot_namespace::RobotKinematicsParameters &kinematicsParameter)
{
    if(protoRobotKinematicsParameter.da_size()!=6) return false;
    if(protoRobotKinematicsParameter.dd_size()!=6) return false;
    if(protoRobotKinematicsParameter.dalpha_size()!=6) return false;
    if(protoRobotKinematicsParameter.dbeta_size()!=6) return false;
    if(protoRobotKinematicsParameter.dratio_size()!=6) return false;
    if(protoRobotKinematicsParameter.dtheta_size()!=6) return false;


    for(int i=0;i<6;i++)
    {
        kinematicsParameter.da[i]     =(int16)protoRobotKinematicsParameter.da(i);
        kinematicsParameter.dd[i]     =(int16)protoRobotKinematicsParameter.dd(i);
        kinematicsParameter.dalpha[i] =(int16) protoRobotKinematicsParameter.dalpha(i);
        kinematicsParameter.dbeta[i]  =(int16) protoRobotKinematicsParameter.dbeta(i);
        kinematicsParameter.dratio[i] = (int16) protoRobotKinematicsParameter.dratio(i);
        kinematicsParameter.dtheta[i] =(int16) protoRobotKinematicsParameter.dtheta(i);
    }
    return true;

}

bool ProtoEncodeDecode::RobotKinematicsParameter_AuboToProto(const RobotKinematicsParameters &kinematicsParameter, aubo::robot::paramerter::ProtoRobotKinematicsParameter &protoRobotKinematicsParameter)
{
    for(int i=0;i<6;i++)
    {
        protoRobotKinematicsParameter.add_da(kinematicsParameter.da[i]);
        protoRobotKinematicsParameter.add_dd(kinematicsParameter.dd[i]);
        protoRobotKinematicsParameter.add_dalpha(kinematicsParameter.dalpha[i]);
        protoRobotKinematicsParameter.add_dbeta(kinematicsParameter.dbeta[i]);
        protoRobotKinematicsParameter.add_dratio(kinematicsParameter.dratio[i]);
        protoRobotKinematicsParameter.add_dtheta(kinematicsParameter.dtheta[i]);
    }

    return true;
}

bool ProtoEncodeDecode::RobotKinematicsParameter_AuboToProto_Legacy(const RobotKinematicsParameters &kinematicsParameter, aubo::robot::paramerter::ProtoRobotKinematicsParameter_legacy &protoRobotKinematicsParameter)
{
    for(int i=0;i<6;i++)
    {
        protoRobotKinematicsParameter.add_da(kinematicsParameter.da[i]);
        protoRobotKinematicsParameter.add_dd(kinematicsParameter.dd[i]);
        protoRobotKinematicsParameter.add_dalpha(kinematicsParameter.dalpha[i]);
        protoRobotKinematicsParameter.add_dbeta(kinematicsParameter.dbeta[i]);
        protoRobotKinematicsParameter.add_dratio(kinematicsParameter.dratio[i]);
        protoRobotKinematicsParameter.add_dtheta(kinematicsParameter.dtheta[i]);
    }

    return true;
}

bool ProtoEncodeDecode::RobotFrictionParameter_ProtoToAubo(const aubo::robot::paramerter::ProtoRobotFrictionParameter &ProtoRobotFrictionParameter, RobotFrictionParameters &frictionParameters)
{
    if(ProtoRobotFrictionParameter.fl_size()!=6) return false;
    if(ProtoRobotFrictionParameter.fr_size()!=6) return false;
    if(ProtoRobotFrictionParameter.tmp_a_size()!=6) return false;
    if(ProtoRobotFrictionParameter.tmp_b_size()!=6) return false;
    if(ProtoRobotFrictionParameter.posvel_a1_size()!=6) return false;
    if(ProtoRobotFrictionParameter.posvel_b1_size()!=6) return false;
    if(ProtoRobotFrictionParameter.posvel_a2_size()!=6) return false;
    if(ProtoRobotFrictionParameter.posvel_b2_size()!=6) return false;
    if(ProtoRobotFrictionParameter.posvel_c2_size()!=6) return false;
    if(ProtoRobotFrictionParameter.negvel_a1_size()!=6) return false;
    if(ProtoRobotFrictionParameter.negvel_b1_size()!=6) return false;
    if(ProtoRobotFrictionParameter.negvel_a2_size()!=6) return false;
    if(ProtoRobotFrictionParameter.negvel_b2_size()!=6) return false;
    if(ProtoRobotFrictionParameter.negvel_c2_size()!=6) return false;

    for(int i=0;i<6;i++)
    {
        frictionParameters.FL[i] = ProtoRobotFrictionParameter.fl(i);
        frictionParameters.FR[i] = ProtoRobotFrictionParameter.fr(i);
        frictionParameters.tmp_a[i] = ProtoRobotFrictionParameter.tmp_a(i);
        frictionParameters.tmp_b[i] = ProtoRobotFrictionParameter.tmp_b(i);

        frictionParameters.posvel_a1[i] = ProtoRobotFrictionParameter.posvel_a1(i);
        frictionParameters.posvel_b1[i] = ProtoRobotFrictionParameter.posvel_b1(i);
        frictionParameters.posvel_a2[i] = ProtoRobotFrictionParameter.posvel_a2(i);
        frictionParameters.posvel_b2[i] = ProtoRobotFrictionParameter.posvel_b2(i);
        frictionParameters.posvel_c2[i] = ProtoRobotFrictionParameter.posvel_c2(i);

        frictionParameters.negvel_a1[i] = ProtoRobotFrictionParameter.negvel_a1(i);
        frictionParameters.negvel_b1[i] = ProtoRobotFrictionParameter.negvel_b1(i);
        frictionParameters.negvel_a2[i] = ProtoRobotFrictionParameter.negvel_a2(i);
        frictionParameters.negvel_b2[i] = ProtoRobotFrictionParameter.negvel_b2(i);
        frictionParameters.negvel_c2[i] = ProtoRobotFrictionParameter.negvel_c2(i);
    }
    return true;
}

bool ProtoEncodeDecode::RobotFrictionParameter_AuboToProto(const RobotFrictionParameters &frictionParameters, aubo::robot::paramerter::ProtoRobotFrictionParameter &ProtoRobotFrictionParameter)
{
    for(int i=0;i<6;i++)
    {
        ProtoRobotFrictionParameter.add_fl(frictionParameters.FL[i]);
        ProtoRobotFrictionParameter.add_fr(frictionParameters.FR[i]);
        ProtoRobotFrictionParameter.add_tmp_a(frictionParameters.tmp_a[i]);
        ProtoRobotFrictionParameter.add_tmp_b(frictionParameters.tmp_b[i]);

        ProtoRobotFrictionParameter.add_posvel_a1((int32)frictionParameters.posvel_a1[i]);
        ProtoRobotFrictionParameter.add_posvel_b1((int32)frictionParameters.posvel_b1[i]);
        ProtoRobotFrictionParameter.add_posvel_a2((int32)frictionParameters.posvel_a2[i]);
        ProtoRobotFrictionParameter.add_posvel_b2((int32)frictionParameters.posvel_b2[i]);
        ProtoRobotFrictionParameter.add_posvel_c2((int32)frictionParameters.posvel_c2[i]);

        ProtoRobotFrictionParameter.add_negvel_a1((int32)frictionParameters.negvel_a1[i]);
        ProtoRobotFrictionParameter.add_negvel_b1((int32)frictionParameters.negvel_b1[i]);
        ProtoRobotFrictionParameter.add_negvel_a2((int32)frictionParameters.negvel_a2[i]);
        ProtoRobotFrictionParameter.add_negvel_b2((int32)frictionParameters.negvel_b2[i]);
        ProtoRobotFrictionParameter.add_negvel_c2((int32)frictionParameters.negvel_c2[i]);
    }

    return true;
}

bool ProtoEncodeDecode::RobotBaseParameter_ProtoToAubo(const aubo::robot::paramerter::ProtoRobotBaseParameter &protoRobotBaseParameter, RobotBaseParameters &robotBaseParameter)
{
    return ( RobotInfo_ProtoToAubo(protoRobotBaseParameter.robotinfo(),robotBaseParameter.info ) &&
             RobotDynamicsParameters_ProtoToAubo(protoRobotBaseParameter.dynamicsparameter(), robotBaseParameter.dynamicParam) &&
             RobotHandguidingParameter_ProtoToAubo(protoRobotBaseParameter.handguidingparameter(), robotBaseParameter.handguidingParam) &&
             RobotKinematicsParameter_ProtoToAubo(protoRobotBaseParameter.kinematicsparameter(), robotBaseParameter.kinematicsParam) );
}

bool ProtoEncodeDecode::RobotBaseParameter_ProtoToAubo_Legacy(const aubo::robot::paramerter::ProtoRobotBaseParameter_legacy &protoRobotBaseParameter, RobotBaseParameters &robotBaseParameter)
{
    RobotKinematicsParameter_ProtoToAubo_Legacy(protoRobotBaseParameter.kinematicsparameter(), robotBaseParameter.kinematicsParam) ;
    return ( RobotInfo_ProtoToAubo(protoRobotBaseParameter.robotinfo(),robotBaseParameter.info ) &&
             RobotDynamicsParameters_ProtoToAubo(protoRobotBaseParameter.dynamicsparameter(), robotBaseParameter.dynamicParam) &&
             RobotHandguidingParameter_ProtoToAubo(protoRobotBaseParameter.handguidingparameter(), robotBaseParameter.handguidingParam) &&
             RobotKinematicsParameter_ProtoToAubo_Legacy(protoRobotBaseParameter.kinematicsparameter(), robotBaseParameter.kinematicsParam) );
}

bool ProtoEncodeDecode::RobotBaseParameter_AuboToProto(const RobotBaseParameters &robotBaseParameter, aubo::robot::paramerter::ProtoRobotBaseParameter &protoRobotBaseParameter)
{
    return ( RobotInfo_AuboToProto(robotBaseParameter.info, *protoRobotBaseParameter.mutable_robotinfo()) &&
             RobotDynamicsParameters_AuboToProto(robotBaseParameter.dynamicParam, *protoRobotBaseParameter.mutable_dynamicsparameter()) &&
             RobotHandguidingParameter_AuboToProto(robotBaseParameter.handguidingParam, *protoRobotBaseParameter.mutable_handguidingparameter()) &&
             RobotKinematicsParameter_AuboToProto(robotBaseParameter.kinematicsParam, *protoRobotBaseParameter.mutable_kinematicsparameter()));
}

bool ProtoEncodeDecode::RobotBaseParameter_AuboToProto_Legacy(const RobotBaseParameters &robotBaseParameter, aubo::robot::paramerter::ProtoRobotBaseParameter_legacy &protoRobotBaseParameter)
{
    return ( RobotInfo_AuboToProto(robotBaseParameter.info, *protoRobotBaseParameter.mutable_robotinfo()) &&
             RobotDynamicsParameters_AuboToProto(robotBaseParameter.dynamicParam, *protoRobotBaseParameter.mutable_dynamicsparameter()) &&
             RobotHandguidingParameter_AuboToProto(robotBaseParameter.handguidingParam, *protoRobotBaseParameter.mutable_handguidingparameter()) &&
             RobotKinematicsParameter_AuboToProto_Legacy(robotBaseParameter.kinematicsParam, *protoRobotBaseParameter.mutable_kinematicsparameter()));
}

bool ProtoEncodeDecode::RobotJointsParameter_ProtoToAubo(const aubo::robot::paramerter::ProtoRobotJointsParameter &protoRobotJointsParameter, RobotJointsParameter &jointsParameter)
{
    return RobotFrictionParameter_ProtoToAubo(protoRobotJointsParameter.frictionparameter(), jointsParameter.frictionParam);
}

bool ProtoEncodeDecode::RobotJointsParameter_AuboToProto(const RobotJointsParameter &jointsParameter, aubo::robot::paramerter::ProtoRobotJointsParameter &protoRobotJointsParameter)
{
    return RobotFrictionParameter_AuboToProto(jointsParameter.frictionParam, *protoRobotJointsParameter.mutable_frictionparameter());
}

bool ProtoEncodeDecode::resolveRequest_ProtoRobotBaseParameter(const char *buffer, int size, RobotBaseParameters &robotBaseParameter)
{
    bool ret = false;

    aubo::robot::paramerter::ProtoRobotBaseParameter  protoRobotBaseParameter;

    //从数组反序列化
    if (protoRobotBaseParameter.ParseFromArray(buffer,size) == true)
    {
        ret = RobotBaseParameter_ProtoToAubo(protoRobotBaseParameter, robotBaseParameter);
    }
    else
    {
        ret = false;

        W_ERROR("ERROR:resolveRequest_ProtoRobotBaseParameter Parse fail." );
    }

    return ret;
}

bool ProtoEncodeDecode::makeResponse_ProtoRobotBaseParameter(char **ptr, int *size, const RobotBaseParameters &robotBaseParameter, int errorCode, const string &errorMsg)
{
    bool ret = true;

    aubo::robot::paramerter::ProtoRobotBaseParameterResponse protoRobotBaseParameterResponse;

    RobotBaseParameter_AuboToProto(robotBaseParameter, *protoRobotBaseParameterResponse.mutable_baseparameter());

    aubo::robot::communication::ProtoRobotCommonResponse* errorInfo = protoRobotBaseParameterResponse.mutable_errorinfo();
    errorInfo->set_errorcode(errorCode);
    errorInfo->set_errormsg(errorMsg);

    //序列化到数组
    int len = protoRobotBaseParameterResponse.ByteSize();

    char *buffer = new char[len];

    if(protoRobotBaseParameterResponse.SerializeToArray(buffer, len) == true)
    {
        *size = len;

        *ptr  = buffer;
    }
    else
    {
        ret = false;

        *size = 0;

        *ptr  = NULL;

        delete[] buffer;

        W_ERROR("ERROR:makeResponse_ProtoRobotBaseParameter Serialize fail.");
    }

    return ret;
}

bool ProtoEncodeDecode::makeRequest_ProtoRobotBaseParameter(char **ptr, int *size, const RobotBaseParameters &robotBaseParameter, int versionCode)
{
    if(versionCode<=4005000)
    {
        aubo::robot::paramerter::ProtoRobotBaseParameter_legacy  protoRobotBaseParameter;

        RobotBaseParameter_AuboToProto_Legacy(robotBaseParameter, protoRobotBaseParameter);

        return ProtobufDataSerializeToArray(&protoRobotBaseParameter, ptr, size);
    }else {
        aubo::robot::paramerter::ProtoRobotBaseParameter  protoRobotBaseParameter;

        RobotBaseParameter_AuboToProto(robotBaseParameter, protoRobotBaseParameter);

        return ProtobufDataSerializeToArray(&protoRobotBaseParameter, ptr, size);
    }
}

bool ProtoEncodeDecode::ParseResponse_ProtoRobotBaseParameter(const char *ptr, int size, RobotBaseParameters &robotBaseParameter, int &errorCode, int versionCode)
{

    bool ret = true;
    if(versionCode<=4005000)
    {
        aubo::robot::paramerter::ProtoRobotBaseParameterResponse_legacy protoRobotBaseParameterResponse;
        //从数组反序列化
        if (protoRobotBaseParameterResponse.ParseFromArray(ptr,size) == true)
        {
            RobotBaseParameter_ProtoToAubo_Legacy(protoRobotBaseParameterResponse.baseparameter(), robotBaseParameter);

            errorCode  = protoRobotBaseParameterResponse.errorinfo().errorcode();
        }
        else
        {
            ret = false;

            std::cerr << "ERROR:ParseResponse_ProtoRobotBaseParameter Parse fail." << std::endl;
        }
    }else{
        aubo::robot::paramerter::ProtoRobotBaseParameterResponse protoRobotBaseParameterResponse;
        //从数组反序列化
        if (protoRobotBaseParameterResponse.ParseFromArray(ptr,size) == true)
        {
            RobotBaseParameter_ProtoToAubo(protoRobotBaseParameterResponse.baseparameter(), robotBaseParameter);

            errorCode  = protoRobotBaseParameterResponse.errorinfo().errorcode();
        }
        else
        {
            ret = false;

            std::cerr << "ERROR:ParseResponse_ProtoRobotBaseParameter Parse fail." << std::endl;
        }
    }
    return ret;
}

bool ProtoEncodeDecode::resolveRequest_ProtoRobotJointsParameter(const char *buffer, int size, RobotJointsParameter &robotJointsParameter)
{
    bool ret = false;

    aubo::robot::paramerter::ProtoRobotJointsParameter  protoRobotJointsParameter;

    //从数组反序列化
    if (protoRobotJointsParameter.ParseFromArray(buffer,size) == true)
    {
        ret = RobotJointsParameter_ProtoToAubo(protoRobotJointsParameter, robotJointsParameter);
    }
    else
    {
        ret = false;

        W_ERROR("ERROR:resolveRequest_ProtoRobotBaseParameter Parse fail." );
    }

    return ret;
}

bool ProtoEncodeDecode::makeResponse_ProtoRobotJointsParameter(char **ptr, int *size, const RobotJointsParameter &robotJointsParameter, int errorCode, const string &errorMsg)
{
    bool ret = true;

    aubo::robot::paramerter::ProtoRobotJointsParameterResponse protoRobotJointsParameterResponse;

    RobotJointsParameter_AuboToProto(robotJointsParameter, *protoRobotJointsParameterResponse.mutable_jointsparameter());

    aubo::robot::communication::ProtoRobotCommonResponse* errorInfo = protoRobotJointsParameterResponse.mutable_errorinfo();
    errorInfo->set_errorcode(errorCode);
    errorInfo->set_errormsg(errorMsg);

    //序列化到数组
    int len = protoRobotJointsParameterResponse.ByteSize();

    char *buffer = new char[len];

    if(protoRobotJointsParameterResponse.SerializeToArray(buffer, len) == true)
    {
        *size = len;

        *ptr  = buffer;
    }
    else
    {
        ret = false;

        *size = 0;

        *ptr  = NULL;

        delete[] buffer;

        W_ERROR("ERROR:makeResponse_ProtoRobotJointsParameter Serialize fail.");
    }

    return ret;
}

bool ProtoEncodeDecode::makeRequest_ProtoRobotJointsParameter(char **ptr, int *size, const RobotJointsParameter &robotJointsParameter)
{
    aubo::robot::paramerter::ProtoRobotJointsParameter  protoRobotJointsParameter;

    RobotJointsParameter_AuboToProto(robotJointsParameter, protoRobotJointsParameter);

    return ProtobufDataSerializeToArray(&protoRobotJointsParameter, ptr, size);
}

bool ProtoEncodeDecode::ParseResponse_ProtoRobotJointsParameter(const char *ptr, int size, RobotJointsParameter &robotJointsParameter, int &errorCode)
{
    bool ret = true;

    aubo::robot::paramerter::ProtoRobotJointsParameterResponse protoRobotJointsParameterResponse;

    //从数组反序列化
    if (protoRobotJointsParameterResponse.ParseFromArray(ptr,size) == true)
    {
        RobotJointsParameter_ProtoToAubo(protoRobotJointsParameterResponse.jointsparameter(), robotJointsParameter);

        errorCode  = protoRobotJointsParameterResponse.errorinfo().errorcode();
    }
    else
    {
        ret = false;

        std::cerr << "ERROR:ParseResponse_ProtoRobotBaseParameter Parse fail." << std::endl;
    }

    return ret;
}


